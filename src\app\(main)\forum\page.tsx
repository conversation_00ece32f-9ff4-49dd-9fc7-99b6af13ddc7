import { Suspense } from 'react'
import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  MessageSquare,
  ThumbsUp,
  Eye,
  Clock,
  TrendingUp,
  Users,
  Briefcase,
  Coffee,
  Plus,
} from 'lucide-react'

/**
 * 论坛首页
 * 展示热门话题、最新帖子和分类讨论
 */
export default function ForumPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题和发帖按钮 */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">社区论坛</h1>
          <p className="text-gray-600">与职场同行交流经验，分享见解</p>
        </div>
        <Button asChild>
          <Link href="/forum/create">
            <Plus className="mr-2 h-4 w-4" />
            发布帖子
          </Link>
        </Button>
      </div>

      {/* 话题分类标签 */}
      <div className="flex flex-wrap gap-2 mb-8">
        <Badge
          variant="secondary"
          className="cursor-pointer hover:bg-secondary/80"
        >
          <TrendingUp className="mr-1 h-3 w-3" />
          热门话题
        </Badge>
        <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
          <Briefcase className="mr-1 h-3 w-3" />
          求职招聘
        </Badge>
        <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
          <Users className="mr-1 h-3 w-3" />
          职场关系
        </Badge>
        <Badge variant="outline" className="cursor-pointer hover:bg-secondary">
          <Coffee className="mr-1 h-3 w-3" />
          生活平衡
        </Badge>
      </div>

      {/* 内容标签页 */}
      <Tabs defaultValue="hot" className="space-y-4">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="hot">热门</TabsTrigger>
          <TabsTrigger value="latest">最新</TabsTrigger>
          <TabsTrigger value="following">关注</TabsTrigger>
        </TabsList>

        {/* 热门内容 */}
        <TabsContent value="hot" className="space-y-4">
          <Suspense fallback={<PostListSkeleton />}>
            <PostList type="hot" />
          </Suspense>
        </TabsContent>

        {/* 最新内容 */}
        <TabsContent value="latest" className="space-y-4">
          <Suspense fallback={<PostListSkeleton />}>
            <PostList type="latest" />
          </Suspense>
        </TabsContent>

        {/* 关注内容 */}
        <TabsContent value="following" className="space-y-4">
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-600 mb-4">登录后查看关注用户的动态</p>
            <Button asChild>
              <Link href="/auth/login">立即登录</Link>
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 帖子列表组件
 * @param type - 列表类型：hot（热门）、latest（最新）
 */
function PostList({ type }: { type: 'hot' | 'latest' }) {
  // 模拟数据 - 根据类型可以加载不同的数据
  // TODO: 后续根据 type 参数从 API 获取不同的数据
  const posts =
    type === 'hot'
      ? [
          {
            id: 1,
            title: '大厂裁员潮下，普通程序员该如何应对？',
            content:
              '最近互联网行业裁员消息不断，作为普通开发者，我们应该如何提升自己的竞争力...',
            author: '匿名用户',
            category: '职场讨论',
            likes: 256,
            comments: 89,
            views: 3420,
            createdAt: '2小时前',
            tags: ['职业规划', '互联网', '裁员'],
          },
          {
            id: 2,
            title: '分享一下我在字节跳动的面试经历',
            content:
              '上周参加了字节的面试，整体感觉还不错。一共四轮，包括算法、系统设计...',
            author: '面试达人',
            category: '面经分享',
            likes: 189,
            comments: 45,
            views: 2100,
            createdAt: '5小时前',
            tags: ['面经', '字节跳动', '算法'],
          },
          {
            id: 3,
            title: '工作三年，是继续打工还是创业？',
            content:
              '在大厂工作了三年，最近有朋友邀请一起创业。很纠结是否要放弃稳定的工作...',
            author: '迷茫的打工人',
            category: '职业规划',
            likes: 134,
            comments: 67,
            views: 1890,
            createdAt: '8小时前',
            tags: ['创业', '职业选择', '大厂'],
          },
          {
            id: 4,
            title: '如何优雅地拒绝加班？',
            content:
              '公司最近项目紧张，天天加班到很晚。想问问大家都是怎么处理加班问题的...',
            author: '不想996',
            category: '职场关系',
            likes: 298,
            comments: 102,
            views: 4560,
            createdAt: '1天前',
            tags: ['加班', '工作生活平衡', '职场'],
          },
        ]
      : [
          // 最新帖子的模拟数据
          {
            id: 5,
            title: '春招面试技巧分享',
            content: '马上就要春招了，分享一些面试准备的经验...',
            author: '求职小白',
            category: '面经分享',
            likes: 45,
            comments: 12,
            views: 567,
            createdAt: '刚刚',
            tags: ['春招', '面试技巧', '求职'],
          },
          {
            id: 6,
            title: '远程办公的利与弊',
            content: '疫情后公司开始推行远程办公，想听听大家的看法...',
            author: '远程工作者',
            category: '职场讨论',
            likes: 78,
            comments: 23,
            views: 890,
            createdAt: '10分钟前',
            tags: ['远程办公', '工作方式', '效率'],
          },
        ]

  return (
    <div className="space-y-4">
      {posts.map(post => (
        <Card key={post.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <CardTitle className="text-lg hover:text-primary cursor-pointer">
                  <Link href={`/forum/${post.id}`}>{post.title}</Link>
                </CardTitle>
                <p className="text-sm text-gray-600 line-clamp-2">
                  {post.content}
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* 标签 */}
            <div className="flex flex-wrap gap-2 mb-4">
              {post.tags.map(tag => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>

            {/* 帖子元信息 */}
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center gap-4">
                <span>{post.author}</span>
                <span>•</span>
                <span>{post.category}</span>
                <span>•</span>
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {post.createdAt}
                </span>
              </div>
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <ThumbsUp className="h-3 w-3" />
                  {post.likes}
                </span>
                <span className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  {post.comments}
                </span>
                <span className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  {post.views}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* 加载更多按钮 */}
      <div className="text-center pt-4">
        <Button variant="outline">加载更多</Button>
      </div>
    </div>
  )
}

/**
 * 帖子列表骨架屏
 */
function PostListSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3].map(i => (
        <Card key={i} className="animate-pulse">
          <CardHeader>
            <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
