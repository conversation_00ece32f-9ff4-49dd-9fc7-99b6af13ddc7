# WorkMates Supabase 接入配置指南

## 📋 概述

本文档专门介绍如何在 WorkMates 项目中接入和配置 Supabase 服务，包括数据库连接、身份认证、API 调用等完整流程。

## 🔗 项目信息

### 当前 Supabase 项目配置
- **项目名称**: WorkMates
- **项目ID**: zfctpeukxaxftfsmpqgp
- **状态**: ACTIVE_HEALTHY
- **区域**: ap-northeast-1
- **数据库**: PostgreSQL 15.1

### 连接信息
```env
# Supabase 项目 URL
NEXT_PUBLIC_SUPABASE_URL=https://zfctpeukxaxftfsmpqgp.supabase.co

# Supabase 匿名密钥
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CuPOCJ-RkxXNQb3A7yeTuS0WLtXQ6tF7gkUUEkO3Dx0

# 数据库直连 URL
DATABASE_URL=postgresql://postgres.zfctpeukxaxftfsmpqgp:<EMAIL>:6543/postgres

# Prisma 直连 URL
DIRECT_URL=postgresql://postgres.zfctpeukxaxftfsmpqgp:<EMAIL>:6543/postgres
```

## 🛠️ 环境配置

### 1. 环境变量设置
创建 `.env.local` 文件：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://zfctpeukxaxftfsmpqgp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CuPOCJ-RkxXNQb3A7yeTuS0WLtXQ6tF7gkUUEkO3Dx0

# 数据库配置
DATABASE_URL=postgresql://postgres.zfctpeukxaxftfsmpqgp:<EMAIL>:6543/postgres
DIRECT_URL=postgresql://postgres.zfctpeukxaxftfsmpqgp:<EMAIL>:6543/postgres

# NextAuth.js 配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Supabase 服务角色密钥（仅服务端使用）
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 2. 依赖安装
```bash
# 安装 Supabase 客户端
npm install @supabase/supabase-js

# 安装 Prisma
npm install prisma @prisma/client
npm install -D prisma

# 安装 NextAuth.js
npm install next-auth
npm install @auth/prisma-adapter
```

## 🔧 客户端配置

### 1. Supabase 客户端初始化
创建 `src/lib/supabase.ts`：

```typescript
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseKey)

// 服务端客户端（用于 API 路由）
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)
```

### 2. 数据库类型生成
```bash
# 生成 TypeScript 类型
npx supabase gen types typescript --project-id zfctpeukxaxftfsmpqgp > src/types/database.ts
```

### 3. Prisma 配置
更新 `prisma/schema.prisma`：

```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// 模型定义...
```

## 🔐 身份认证配置

### 1. NextAuth.js 配置
创建 `src/app/api/auth/[...nextauth]/route.ts`：

```typescript
import NextAuth from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import { prisma } from '@/lib/prisma'
import GoogleProvider from 'next-auth/providers/google'
import EmailProvider from 'next-auth/providers/email'

const handler = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: process.env.EMAIL_SERVER_PORT,
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
    }),
  ],
  pages: {
    signIn: '/auth/login',
    signUp: '/auth/register',
  },
  callbacks: {
    session: ({ session, token }) => ({
      ...session,
      user: {
        ...session.user,
        id: token.sub,
      },
    }),
  },
})

export { handler as GET, handler as POST }
```

### 2. 认证提供者配置
在 `src/components/providers.tsx` 中：

```typescript
'use client'

import { SessionProvider } from 'next-auth/react'
import { ReactNode } from 'react'

interface ProvidersProps {
  children: ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <SessionProvider>
      {children}
    </SessionProvider>
  )
}
```

## 📊 数据库操作

### 1. 基本 CRUD 操作
```typescript
// 创建记录
export async function createCompany(data: CompanyCreateInput) {
  const { data: company, error } = await supabase
    .from('companies')
    .insert(data)
    .select()
    .single()

  if (error) throw error
  return company
}

// 查询记录
export async function getCompanies(filters?: CompanyFilters) {
  let query = supabase
    .from('companies')
    .select('*')

  if (filters?.name) {
    query = query.ilike('name', `%${filters.name}%`)
  }

  const { data, error } = await query
  if (error) throw error
  return data
}

// 更新记录
export async function updateCompany(id: string, data: CompanyUpdateInput) {
  const { data: company, error } = await supabase
    .from('companies')
    .update(data)
    .eq('id', id)
    .select()
    .single()

  if (error) throw error
  return company
}

// 删除记录
export async function deleteCompany(id: string) {
  const { error } = await supabase
    .from('companies')
    .delete()
    .eq('id', id)

  if (error) throw error
}
```

### 2. 实时订阅
```typescript
// 监听数据变化
export function subscribeToCompanies(callback: (companies: Company[]) => void) {
  const channel = supabase
    .channel('companies')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'companies',
      },
      (payload) => {
        console.log('Change received!', payload)
        // 重新获取数据
        getCompanies().then(callback)
      }
    )
    .subscribe()

  return () => {
    supabase.removeChannel(channel)
  }
}
```

### 3. 文件存储
```typescript
// 上传文件
export async function uploadFile(file: File, bucket: string, path: string) {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file)

  if (error) throw error
  return data
}

// 获取文件 URL
export function getFileUrl(bucket: string, path: string) {
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path)

  return data.publicUrl
}
```

## 🔒 行级安全策略 (RLS)

### 1. 启用 RLS
```sql
-- 启用行级安全
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
```

### 2. 创建策略
```sql
-- 公司信息可读策略
CREATE POLICY "Companies are viewable by everyone"
ON companies FOR SELECT
USING (true);

-- 用户只能编辑自己的帖子
CREATE POLICY "Users can update their own posts"
ON posts FOR UPDATE
USING (auth.uid() = author_id);

-- 用户只能删除自己的评论
CREATE POLICY "Users can delete their own comments"
ON comments FOR DELETE
USING (auth.uid() = author_id);
```

## 🧪 测试配置

### 1. 连接测试
```typescript
// 测试数据库连接
export async function testDatabaseConnection() {
  try {
    const { data, error } = await supabase
      .from('companies')
      .select('count')
      .limit(1)

    if (error) throw error
    console.log('数据库连接成功')
    return true
  } catch (error) {
    console.error('数据库连接失败:', error)
    return false
  }
}
```

### 2. 认证测试
```typescript
// 测试认证功能
export async function testAuthentication() {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    console.log('当前用户:', user)
    return user
  } catch (error) {
    console.error('认证测试失败:', error)
    return null
  }
}
```

## 🚀 部署配置

### 1. 生产环境变量
```env
# 生产环境配置
NEXT_PUBLIC_SUPABASE_URL=https://zfctpeukxaxftfsmpqgp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
DATABASE_URL=your-production-db-url
DIRECT_URL=your-production-direct-url
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-production-secret
```

### 2. Vercel 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署到 Vercel
vercel --prod
```

## 🛡️ 故障排除

### 常见问题

#### 1. 数据库连接失败
- 检查环境变量是否正确
- 确认 Supabase 项目状态
- 验证 IP 白名单设置

#### 2. 认证问题
- 检查 NextAuth.js 配置
- 确认回调 URL 设置
- 验证认证提供者配置

#### 3. 权限问题
- 检查 RLS 策略
- 确认用户角色设置
- 验证 API 密钥权限

### 调试工具
```bash
# 查看 Supabase 状态
npx supabase status

# 查看数据库结构
npx prisma studio

# 测试连接
node -e "require('./src/lib/supabase').testDatabaseConnection()"
```

## 📚 相关资源

- [Supabase 官方文档](https://supabase.com/docs)
- [Prisma 文档](https://www.prisma.io/docs)
- [NextAuth.js 文档](https://next-auth.js.org/)
- [数据库设计文档](./WorkMates数据库完整设计文档.md)

## 📝 最佳实践

1. **安全性**
   - 始终使用环境变量存储敏感信息
   - 启用行级安全策略
   - 定期更新 API 密钥

2. **性能**
   - 使用连接池
   - 实施查询优化
   - 启用缓存机制

3. **维护性**
   - 定期备份数据库
   - 监控数据库性能
   - 保持依赖项更新 