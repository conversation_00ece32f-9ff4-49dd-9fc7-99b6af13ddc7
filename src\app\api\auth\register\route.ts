import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

const registerSchema = z.object({
  name: z.string().min(2, '用户名至少2个字符'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6个字符'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, password } = registerSchema.parse(body)
    
    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
      include: { accounts: true }
    })
    
    if (existingUser) {
      // 检查是否有Google账号
      const hasGoogleAccount = existingUser.accounts.some(
        (acc: any) => acc.provider === 'google'
      )
      
      if (hasGoogleAccount) {
        return NextResponse.json({
          success: false,
          error: {
            code: 'EMAIL_HAS_GOOGLE_ACCOUNT',
            message: '此邮箱已关联Google账号，请使用Google登录'
          }
        }, { status: 409 })
      } else {
        return NextResponse.json({
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: '此邮箱已注册，请直接登录'
          }
        }, { status: 409 })
      }
    }
    
    // 创建新用户
    const hashedPassword = await bcrypt.hash(password, 12)
    
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
      }
    })
    
    // 创建credentials账号记录
    await prisma.account.create({
      data: {
        userId: user.id,
        type: 'credentials',
        provider: 'credentials',
        providerAccountId: user.id,
      }
    })
    
    return NextResponse.json({
      success: true,
      message: '注册成功',
      data: {
        id: user.id,
        name: user.name,
        email: user.email,
      }
    })
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '输入数据验证失败',
          details: error.errors
        }
      }, { status: 400 })
    }
    
    console.error('Registration error:', error)
    return NextResponse.json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '注册失败，请稍后重试'
      }
    }, { status: 500 })
  }
} 