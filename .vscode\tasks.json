{"version": "2.0.0", "tasks": [{"label": "🚀 启动开发服务器", "type": "npm", "script": "dev", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [{"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "ready - started server on .+, url: (https?://.+)"}}}], "isBackground": true}, {"label": "🔨 构建项目", "type": "npm", "script": "build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "🧹 代码格式化", "type": "shell", "command": "npm", "args": ["run", "lint", "--", "--fix"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "🔍 类型检查", "type": "npm", "script": "type-check", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "🗄️ 数据库推送", "type": "npm", "script": "db:push", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🔄 数据库迁移", "type": "npm", "script": "db:migrate", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "📊 Prisma Studio", "type": "npm", "script": "db:studio", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true}, {"label": "🌱 数据库种子", "type": "npm", "script": "db:seed", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🧪 运行测试", "type": "shell", "command": "npm", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated"}}, {"label": "🔄 重新安装依赖", "type": "shell", "command": "npm", "args": ["ci"], "options": {"cwd": "${workspaceFolder}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}