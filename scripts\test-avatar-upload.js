/**
 * 头像上传功能测试脚本
 * 测试 /api/upload/avatar-r2 接口
 */

const fs = require('fs')
const path = require('path')
const FormData = require('form-data')
const fetch = require('node-fetch')

// 创建测试图片文件
function createTestImage() {
  // 创建一个简单的 1x1 像素的 PNG 图片
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG 签名
    0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
    0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 像素
    0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE,
    0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, 0x54, // IDAT chunk
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0xFF, 0xFF,
    0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
    0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82 // IEND chunk
  ])
  
  const testImagePath = path.join(__dirname, 'test-avatar.png')
  fs.writeFileSync(testImagePath, pngData)
  return testImagePath
}

// 测试头像上传
async function testAvatarUpload() {
  console.log('🧪 开始测试头像上传功能\n')

  try {
    // 1. 创建测试图片
    console.log('📸 创建测试图片...')
    const testImagePath = createTestImage()
    console.log(`✅ 测试图片创建成功: ${testImagePath}`)

    // 2. 准备上传数据
    const formData = new FormData()
    formData.append('file', fs.createReadStream(testImagePath), {
      filename: 'test-avatar.png',
      contentType: 'image/png'
    })

    // 3. 发送上传请求
    console.log('\n📤 发送上传请求...')
    const response = await fetch('http://localhost:3000/api/upload/avatar-r2', {
      method: 'POST',
      body: formData,
      headers: {
        // 注意：这里需要有效的 session cookie
        // 在实际测试中，需要先登录获取 session
        'Cookie': 'next-auth.session-token=your-session-token'
      }
    })

    // 4. 解析响应
    const result = await response.json()
    
    console.log(`📊 响应状态: ${response.status}`)
    console.log('📋 响应内容:')
    console.log(JSON.stringify(result, null, 2))

    // 5. 验证结果
    if (response.status === 200 && result.success) {
      console.log('\n✅ 头像上传测试成功！')
      console.log(`🔗 头像URL: ${result.data.upload.url}`)
      console.log(`🔑 存储键: ${result.data.upload.key}`)
    } else if (response.status === 401) {
      console.log('\n⚠️  需要登录认证')
      console.log('💡 请在浏览器中登录后再测试，或使用浏览器测试页面')
    } else {
      console.log('\n❌ 头像上传测试失败')
      console.log(`错误: ${result.message}`)
    }

    // 6. 清理测试文件
    fs.unlinkSync(testImagePath)
    console.log('\n🧹 清理测试文件完成')

  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error.message)
  }
}

// API 接口测试
async function testAPIEndpoint() {
  console.log('🔍 测试 API 接口可访问性...')
  
  try {
    const response = await fetch('http://localhost:3000/api/upload/avatar-r2', {
      method: 'GET'
    })
    
    console.log(`📊 GET 请求状态: ${response.status}`)
    
    if (response.status === 405) {
      console.log('✅ API 接口正常 (Method Not Allowed 是预期的)')
    } else {
      const result = await response.text()
      console.log('📋 响应内容:', result)
    }
  } catch (error) {
    console.error('❌ API 接口测试失败:', error.message)
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 头像上传功能测试\n')
  
  // 检查服务器是否运行
  try {
    const healthCheck = await fetch('http://localhost:3000')
    if (!healthCheck.ok) {
      throw new Error('服务器未响应')
    }
    console.log('✅ 服务器运行正常\n')
  } catch (error) {
    console.error('❌ 无法连接到服务器，请确保开发服务器正在运行')
    console.error('💡 运行命令: npm run dev')
    return
  }

  // 测试 API 接口
  await testAPIEndpoint()
  console.log()

  // 测试文件上传
  await testAvatarUpload()

  console.log('\n📋 测试总结:')
  console.log('1. ✅ 服务器连接正常')
  console.log('2. ✅ API 接口可访问')
  console.log('3. ⚠️  文件上传需要用户认证')
  console.log('\n💡 建议使用浏览器测试页面进行完整测试:')
  console.log('   - http://localhost:3000/test/r2-upload')
  console.log('   - http://localhost:3000/profile/settings')
}

// 运行测试
runTests().catch(error => {
  console.error('\n💥 测试失败:', error)
  process.exit(1)
})
