{
  // ===== 编辑器设置 =====
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.rulers": [80, 120],
  "editor.wordWrap": "on",
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,

  // ===== TypeScript 设置 =====
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.includePackageJsonAutoImports": "auto",

  // ===== 文件关联设置 =====
  "files.associations": {
    "*.css": "tailwindcss",
    ".env*": "dotenv"
  },

  // ===== Emmet 设置 =====
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },

  // ===== 调试设置 =====
  "debug.node.autoAttach": "smart",
  "debug.javascript.autoAttachFilter": "smart",
  "debug.terminal.clearBeforeReusing": true,

  // ===== 搜索排除设置 =====
  "search.exclude": {
    "**/node_modules": true,
    "**/.next": true,
    "**/dist": true,
    "**/.cache": true,
    "**/coverage": true
  },

  // ===== 文件监视排除设置 =====
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/dist/**": true
  },

  // ===== Git 设置 =====
  "git.ignoreLimitWarning": true,
  "git.autofetch": true,

  // ===== 扩展特定设置 =====
  "eslint.workingDirectories": ["./"],
  "prettier.configPath": "./.prettierrc",
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],

  // ===== 终端设置 =====
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.profiles.windows": {
    "PowerShell": {
      "source": "PowerShell",
      "icon": "terminal-powershell"
    },
    "Command Prompt": {
      "path": [
        "${env:windir}\\Sysnative\\cmd.exe",
        "${env:windir}\\System32\\cmd.exe"
      ],
      "args": [],
      "icon": "terminal-cmd"
    }
  },

  // ===== 智能感知设置 =====
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.parameterTypes.enabled": true,
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.inlayHints.propertyDeclarationTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,

  // ===== 调试控制台设置 =====
  "debug.console.fontSize": 14,
  "debug.console.fontFamily": "Consolas, 'Courier New', monospace"
} 