import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { unlink } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'

/**
 * 获取文件详情
 * GET /api/upload/[id]
 *
 * 需要用户登录且为文件所有者
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再查看文件信息',
          },
        },
        { status: 401 }
      )
    }

    const { id } = await params

    // 查找工作文件（ExperienceFile）
    const experienceFile = await prisma.experienceFile.findFirst({
      where: {
        id,
        workExperience: {
          userId: session.user.id,
        },
      },
      select: {
        id: true,
        fileName: true,
        fileUrl: true,
        fileSize: true,
        mimeType: true,
        description: true,
        category: true,
        verificationStatus: true,
        uploadedAt: true,
        createdAt: true,
        workExperience: {
          select: {
            id: true,
            companyName: true,
            position: true,
          },
        },
      },
    })

    if (!experienceFile) {
      return NextResponse.json(
        {
          success: false,
          message: '文件不存在',
          error: {
            code: 'FILE_NOT_FOUND',
            message: '指定的文件不存在或无权访问',
          },
        },
        { status: 404 }
      )
    }

    // 检查文件是否在磁盘上存在
    let fileExists = false
    if (experienceFile.fileUrl.startsWith('/uploads/')) {
      const filePath = path.join(
        process.cwd(),
        'public',
        experienceFile.fileUrl
      )
      fileExists = existsSync(filePath)
    }

    return NextResponse.json({
      success: true,
      message: '获取文件信息成功',
      data: {
        ...experienceFile,
        fileExists,
        downloadUrl: experienceFile.fileUrl,
        humanReadableSize: formatFileSize(experienceFile.fileSize || 0),
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取文件信息失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取文件信息失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 删除文件
 * DELETE /api/upload/[id]
 *
 * 需要用户登录且为文件所有者
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再删除文件',
          },
        },
        { status: 401 }
      )
    }

    const { id } = await params

    // 查找文件并验证权限
    const experienceFile = await prisma.experienceFile.findFirst({
      where: {
        id,
        workExperienceId: {
          in: await prisma.workExperience
            .findMany({
              where: { userId: session.user.id },
              select: { id: true },
            })
            .then(results => results.map(r => r.id)),
        },
      },
      select: {
        id: true,
        fileName: true,
        fileUrl: true,
        verificationStatus: true,
        workExperience: {
          select: {
            companyName: true,
            position: true,
          },
        },
      },
    })

    if (!experienceFile) {
      return NextResponse.json(
        {
          success: false,
          message: '文件不存在',
          error: {
            code: 'FILE_NOT_FOUND',
            message: '指定的文件不存在或无权访问',
          },
        },
        { status: 404 }
      )
    }

    // 检查文件是否已通过验证（可选限制）
    if (experienceFile.verificationStatus === 'APPROVED') {
      const url = new URL(request.url)
      const force = url.searchParams.get('force') === 'true'

      if (!force) {
        return NextResponse.json(
          {
            success: false,
            message: '无法删除已验证的文件',
            error: {
              code: 'CANNOT_DELETE_VERIFIED_FILE',
              message:
                '已通过验证的文件无法删除，如需删除请联系管理员或使用 force=true 参数',
            },
          },
          { status: 403 }
        )
      }
    }

    // 删除磁盘上的文件
    if (experienceFile.fileUrl.startsWith('/uploads/')) {
      try {
        const filePath = path.join(
          process.cwd(),
          'public',
          experienceFile.fileUrl
        )
        if (existsSync(filePath)) {
          await unlink(filePath)
        }
      } catch (error) {
        console.warn('删除磁盘文件失败:', error)
        // 继续执行数据库删除，不因磁盘删除失败而中断
      }
    }

    // 从数据库删除文件记录
    await prisma.experienceFile.delete({
      where: { id },
    })

    // 记录删除操作（可选）
    console.log(
      `用户 ${session.user.id} 删除了文件: ${experienceFile.fileName}`
    )

    return NextResponse.json({
      success: true,
      message: '文件删除成功',
      data: {
        deletedFile: {
          id: experienceFile.id,
          fileName: experienceFile.fileName,
          workExperience: experienceFile.workExperience,
        },
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('删除文件失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '删除文件失败',
        error: {
          code: 'DELETE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新文件信息
 * PATCH /api/upload/[id]
 *
 * 需要用户登录且为文件所有者
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再更新文件信息',
          },
        },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    // 验证文件是否存在且属于当前用户
    const existingFile = await prisma.experienceFile.findFirst({
      where: {
        id,
        workExperience: {
          userId: session.user.id,
        },
      },
    })

    if (!existingFile) {
      return NextResponse.json(
        {
          success: false,
          message: '文件不存在',
          error: {
            code: 'FILE_NOT_FOUND',
            message: '指定的文件不存在或无权访问',
          },
        },
        { status: 404 }
      )
    }

    // 只允许更新描述字段
    const allowedFields = ['description']
    const updateData: any = {}

    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        if (field === 'description' && body[field].length > 500) {
          return NextResponse.json(
            {
              success: false,
              message: '描述过长',
              error: {
                code: 'DESCRIPTION_TOO_LONG',
                message: '描述不能超过500个字符',
              },
            },
            { status: 400 }
          )
        }
        updateData[field] = body[field]
      }
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '没有提供要更新的数据',
          error: {
            code: 'NO_UPDATE_DATA',
            message: '请提供要更新的字段（description）',
          },
        },
        { status: 400 }
      )
    }

    // 更新文件信息
    const updatedFile = await prisma.experienceFile.update({
      where: { id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        fileName: true,
        description: true,
        updatedAt: true,
        workExperience: {
          select: {
            companyName: true,
            position: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      message: '文件信息更新成功',
      data: updatedFile,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('更新文件信息失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新文件信息失败',
        error: {
          code: 'UPDATE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
