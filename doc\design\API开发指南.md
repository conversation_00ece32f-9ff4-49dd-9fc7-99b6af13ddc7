# WorkMates API开发指南

_最后更新时间：2025年7月13日_

## 📋 概述

本文档定义了WorkMates项目的API开发标准，包括设计原则、命名规范、错误处理、认证授权、以及API文档化标准。

## 🚀 已实现API状态 (更新至2025年7月13日)

### ✅ 已完成并测试的API

#### 🔐 用户认证系统 (NextAuth.js v5)

- `GET/POST /api/auth/[...nextauth]` - NextAuth.js 认证处理 ✅
- `GET /api/debug/session` - 会话调试接口 ✅

#### 🏢 企业信息API

- `GET /api/companies` - 企业列表查询 ✅
- `GET /api/companies/[id]` - 企业详情查询 ✅ (包含评价、薪资、面试数据)
- `PATCH /api/companies/[id]` - 企业信息更新 ✅
- `GET /api/search/companies` - 企业搜索 ✅

#### 👤 用户管理API

- `GET /api/users/profile` - 用户个人信息 ✅

#### 💬 论坛系统API

- `POST /api/posts` - 帖子创建 ✅
- `GET /api/posts` - 帖子列表 (基础实现)

### 🔄 开发中的API

#### 📤 文件上传系统API

- `POST /api/upload` - 文件上传 (Cloudflare R2 配置中)
- `POST /api/upload/avatar` - 头像上传 (开发中)

#### 🔍 搜索系统API

- `GET /api/search` - 全局搜索 (计划中)
- `GET /api/search/posts` - 帖子搜索 (计划中)

### ⏳ 计划开发的API

#### 💬 论坛扩展API

- `GET/PATCH/DELETE /api/posts/[id]` - 帖子管理
- `GET/POST /api/posts/[id]/comments` - 评论系统
- `POST /api/posts/[id]/like` - 点赞功能

#### 📊 数据提交API

- `POST /api/companies/[id]/reviews` - 企业评价提交
- `POST /api/companies/[id]/salaries` - 薪资数据提交
- `POST /api/companies/[id]/interviews` - 面试经验提交
- `GET /api/search/users` - 用户搜索（隐私保护）

### 📋 待开发的API

- 用户统计: `/api/users/[id]/stats`
- 管理系统: `/api/admin/*`

## 🎯 设计原则

### RESTful 设计

遵循REST架构风格，使用标准HTTP方法和状态码

### 一致性

保持API接口的一致性，包括命名、结构和响应格式

### 向后兼容

确保API版本升级时的向后兼容性

### 安全性

实施适当的认证、授权和数据验证

## 📁 API目录结构

```
src/app/api/
├── auth/                    # 认证相关API
│   ├── login/route.ts       # 用户登录
│   ├── register/route.ts    # 用户注册
│   ├── logout/route.ts      # 用户登出
│   └── refresh/route.ts     # 刷新令牌
├── companies/               # 公司相关API
│   ├── route.ts             # 公司列表、创建
│   ├── [id]/route.ts        # 公司详情、更新、删除
│   ├── [id]/reviews/route.ts # 公司评价
│   └── [id]/salaries/route.ts # 薪资数据
├── users/                   # 用户相关API
│   ├── route.ts             # 用户列表
│   ├── [id]/route.ts        # 用户详情
│   ├── profile/route.ts     # 用户资料
│   └── settings/route.ts    # 用户设置
├── posts/                   # 帖子相关API
│   ├── route.ts             # 帖子列表、创建
│   ├── [id]/route.ts        # 帖子详情、更新、删除
│   └── [id]/comments/route.ts # 帖子评论
├── search/                  # 搜索相关API
│   ├── companies/route.ts   # 搜索公司
│   ├── posts/route.ts       # 搜索帖子
│   └── users/route.ts       # 搜索用户
└── admin/                   # 管理员API
    ├── users/route.ts       # 用户管理
    ├── companies/route.ts   # 公司管理
    └── reports/route.ts     # 举报管理
```

## 🌐 HTTP方法和URL设计

### 资源命名规范

- 使用名词，避免动词
- 使用复数形式
- 使用小写字母和连字符
- 保持层次结构清晰

### HTTP方法使用

| 方法   | 用途         | URL示例                     | 说明             |
| ------ | ------------ | --------------------------- | ---------------- |
| GET    | 获取资源     | `GET /api/companies`        | 获取公司列表     |
| GET    | 获取单个资源 | `GET /api/companies/123`    | 获取特定公司     |
| POST   | 创建资源     | `POST /api/companies`       | 创建新公司       |
| PUT    | 完整更新资源 | `PUT /api/companies/123`    | 完整更新公司信息 |
| PATCH  | 部分更新资源 | `PATCH /api/companies/123`  | 部分更新公司信息 |
| DELETE | 删除资源     | `DELETE /api/companies/123` | 删除公司         |

### URL参数规范

#### 查询参数

```
GET /api/companies?page=1&limit=20&sort=name&filter=tech
GET /api/posts?category=interview&author=123&date_from=2024-01-01
```

#### 路径参数

```
GET /api/companies/123
GET /api/companies/123/reviews
GET /api/users/456/work-experience
```

## 📝 请求和响应格式

### 统一响应格式

```typescript
interface ApiResponse<T> {
  success: boolean
  message: string
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    pagination?: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    timestamp: string
    version: string
  }
}
```

### 成功响应示例

```json
{
  "success": true,
  "message": "获取公司列表成功",
  "data": [
    {
      "id": "123",
      "name": "阿里巴巴",
      "description": "全球最大的电子商务公司之一",
      "averageRating": 4.2,
      "totalRatings": 156
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    },
    "timestamp": "2025-07-06T12:00:00Z",
    "version": "1.0"
  }
}
```

### 错误响应示例

```json
{
  "success": false,
  "message": "请求失败",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": {
      "name": "公司名称不能为空",
      "email": "邮箱格式不正确"
    }
  },
  "meta": {
    "timestamp": "2025-07-06T12:00:00Z",
    "version": "1.0"
  }
}
```

## 🔒 认证和授权

### JWT Token认证

```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify } from 'jose'

export async function middleware(request: NextRequest) {
  const token = request.headers.get('authorization')?.replace('Bearer ', '')

  if (!token) {
    return NextResponse.json(
      { success: false, message: '未提供认证令牌' },
      { status: 401 }
    )
  }

  try {
    const { payload } = await jwtVerify(
      new TextEncoder().encode(token),
      new TextEncoder().encode(process.env.JWT_SECRET!)
    )

    // 将用户信息添加到请求头
    const response = NextResponse.next()
    response.headers.set('x-user-id', payload.sub as string)
    return response
  } catch (error) {
    return NextResponse.json(
      { success: false, message: '无效的认证令牌' },
      { status: 401 }
    )
  }
}
```

### 权限控制

```typescript
// 权限装饰器
export function requireAuth(handler: Function) {
  return async (request: NextRequest) => {
    const userId = request.headers.get('x-user-id')

    if (!userId) {
      return NextResponse.json(
        { success: false, message: '需要登录' },
        { status: 401 }
      )
    }

    return handler(request, userId)
  }
}

// 管理员权限
export function requireAdmin(handler: Function) {
  return async (request: NextRequest) => {
    const userId = request.headers.get('x-user-id')
    const user = await getUserById(userId)

    if (!user || user.role !== 'admin') {
      return NextResponse.json(
        { success: false, message: '需要管理员权限' },
        { status: 403 }
      )
    }

    return handler(request, userId)
  }
}
```

## ⚡ API实现模板

### 基础CRUD模板

```typescript
// src/app/api/companies/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { z } from 'zod'

const prisma = new PrismaClient()

// 创建公司的验证模式
const createCompanySchema = z.object({
  name: z.string().min(1, '公司名称不能为空'),
  description: z.string().optional(),
  website: z.string().url().optional(),
  industry: z.string().optional(),
})

// 获取公司列表
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const search = url.searchParams.get('search')
    const industry = url.searchParams.get('industry')

    // 构建查询条件
    const where: any = {}
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ]
    }
    if (industry) {
      where.industry = industry
    }

    // 执行查询
    const [companies, total] = await Promise.all([
      prisma.company.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        include: {
          _count: {
            select: {
              ratings: true,
              salaries: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.company.count({ where }),
    ])

    return NextResponse.json({
      success: true,
      message: '获取公司列表成功',
      data: companies,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取公司列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取公司列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}

// 创建公司
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // 验证输入数据
    const validatedData = createCompanySchema.parse(body)

    // 检查公司是否已存在
    const existingCompany = await prisma.company.findFirst({
      where: { name: validatedData.name },
    })

    if (existingCompany) {
      return NextResponse.json(
        {
          success: false,
          message: '公司已存在',
          error: {
            code: 'DUPLICATE_COMPANY',
            message: '该公司名称已被使用',
          },
        },
        { status: 409 }
      )
    }

    // 创建公司
    const company = await prisma.company.create({
      data: validatedData,
    })

    return NextResponse.json(
      {
        success: true,
        message: '创建公司成功',
        data: company,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '输入数据格式不正确',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('创建公司失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '创建公司失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
```

### 单个资源操作模板

```typescript
// src/app/api/companies/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 获取单个公司
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const company = await prisma.company.findUnique({
      where: { id: params.id },
      include: {
        ratings: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        salaries: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            ratings: true,
            salaries: true,
            interviews: true,
          },
        },
      },
    })

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: '获取公司详情成功',
      data: company,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取公司详情失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取公司详情失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}

// 更新公司
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()

    // 验证公司是否存在
    const existingCompany = await prisma.company.findUnique({
      where: { id: params.id },
    })

    if (!existingCompany) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    // 更新公司
    const updatedCompany = await prisma.company.update({
      where: { id: params.id },
      data: body,
    })

    return NextResponse.json({
      success: true,
      message: '更新公司成功',
      data: updatedCompany,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('更新公司失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新公司失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}

// 删除公司
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证公司是否存在
    const existingCompany = await prisma.company.findUnique({
      where: { id: params.id },
    })

    if (!existingCompany) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    // 软删除公司（如果需要保留历史数据）
    await prisma.company.update({
      where: { id: params.id },
      data: { deletedAt: new Date() },
    })

    return NextResponse.json({
      success: true,
      message: '删除公司成功',
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('删除公司失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '删除公司失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
```

## 📊 分页和排序

### 标准分页参数

```typescript
interface PaginationParams {
  page?: number // 页码，默认1
  limit?: number // 每页数量，默认20，最大100
  sort?: string // 排序字段
  order?: 'asc' | 'desc' // 排序方向，默认desc
}

// 解析分页参数
function parsePaginationParams(url: URL): PaginationParams {
  return {
    page: Math.max(1, parseInt(url.searchParams.get('page') || '1')),
    limit: Math.min(
      100,
      Math.max(1, parseInt(url.searchParams.get('limit') || '20'))
    ),
    sort: url.searchParams.get('sort') || 'createdAt',
    order: (url.searchParams.get('order') as 'asc' | 'desc') || 'desc',
  }
}
```

## 🛡️ 数据验证

### 使用Zod进行验证

```typescript
import { z } from 'zod'

// 公司创建验证模式
export const createCompanySchema = z.object({
  name: z
    .string()
    .min(1, '公司名称不能为空')
    .max(100, '公司名称不能超过100个字符'),
  description: z.string().max(1000, '公司描述不能超过1000个字符').optional(),
  website: z.string().url('网站地址格式不正确').optional(),
  industry: z
    .enum(['technology', 'finance', 'healthcare', 'education', 'other'])
    .optional(),
  size: z
    .enum(['startup', 'small', 'medium', 'large', 'enterprise'])
    .optional(),
})

// 公司评价验证模式
export const createRatingSchema = z.object({
  companyId: z.string().uuid('无效的公司ID'),
  overallRating: z.number().min(1, '评分不能低于1').max(5, '评分不能高于5'),
  cultureRating: z.number().min(1).max(5),
  compensationRating: z.number().min(1).max(5),
  managementRating: z.number().min(1).max(5),
  workLifeBalance: z.number().min(1).max(5),
  careerGrowth: z.number().min(1).max(5),
  title: z.string().max(200, '标题不能超过200个字符'),
  content: z
    .string()
    .min(10, '评价内容至少10个字符')
    .max(2000, '评价内容不能超过2000个字符'),
  pros: z.string().max(1000).optional(),
  cons: z.string().max(1000).optional(),
  isAnonymous: z.boolean().default(false),
})
```

## 📈 错误处理和日志

### 错误代码规范

```typescript
export enum ErrorCodes {
  // 通用错误
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE_RESOURCE = 'DUPLICATE_RESOURCE',

  // 认证错误
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',

  // 业务错误
  COMPANY_NOT_FOUND = 'COMPANY_NOT_FOUND',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  RATING_ALREADY_EXISTS = 'RATING_ALREADY_EXISTS',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
}
```

### 统一错误处理器

```typescript
export class ApiError extends Error {
  constructor(
    public code: string,
    public message: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error)

  if (error instanceof ApiError) {
    return NextResponse.json(
      {
        success: false,
        message: error.message,
        error: {
          code: error.code,
          message: error.message,
          details: error.details,
        },
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: error.statusCode }
    )
  }

  if (error instanceof z.ZodError) {
    return NextResponse.json(
      {
        success: false,
        message: '输入数据验证失败',
        error: {
          code: ErrorCodes.VALIDATION_ERROR,
          message: '输入数据格式不正确',
          details: error.errors.reduce(
            (acc, err) => {
              acc[err.path.join('.')] = err.message
              return acc
            },
            {} as Record<string, string>
          ),
        },
      },
      { status: 400 }
    )
  }

  return NextResponse.json(
    {
      success: false,
      message: '服务器内部错误',
      error: {
        code: ErrorCodes.INTERNAL_ERROR,
        message: '请稍后重试',
      },
    },
    { status: 500 }
  )
}
```

## 📚 API文档化

### 使用JSDoc注释

```typescript
/**
 * @swagger
 * /api/companies:
 *   get:
 *     summary: 获取公司列表
 *     description: 分页获取公司列表，支持搜索和筛选
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 成功获取公司列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Company'
 */
export async function GET(request: NextRequest) {
  // API实现
}
```

### Swagger/OpenAPI集成

```typescript
// swagger.ts
import swaggerJSDoc from 'swagger-jsdoc'

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'WorkMates API',
      version: '1.0.0',
      description: 'WorkMates职场社区API文档',
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: '开发环境',
      },
      {
        url: 'https://workmates.com',
        description: '生产环境',
      },
    ],
  },
  apis: ['./src/app/api/**/*.ts'],
}

export const swaggerSpec = swaggerJSDoc(options)
```

## ✅ API开发检查清单

### 开发前检查

- [ ] 确定API的业务需求和用例
- [ ] 设计RESTful的URL结构
- [ ] 定义请求和响应数据格式
- [ ] 确定认证和授权需求
- [ ] 规划错误处理策略

### 开发中检查

- [ ] 实现输入数据验证
- [ ] 添加适当的错误处理
- [ ] 实现分页和排序
- [ ] 添加适当的数据库索引
- [ ] 考虑性能优化

### 开发后检查

- [ ] 编写API文档
- [ ] 添加单元测试和集成测试
- [ ] 进行安全性测试
- [ ] 性能测试和优化
- [ ] 代码审查

### 部署前检查

- [ ] 验证所有测试通过
- [ ] 检查日志记录
- [ ] 验证错误处理
- [ ] 确认监控配置
- [ ] 准备回滚方案

## 🎯 性能优化建议

### 数据库优化

```typescript
// 使用索引
await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name)`

// 使用连接池
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
})

// 选择性查询字段
const companies = await prisma.company.findMany({
  select: {
    id: true,
    name: true,
    averageRating: true,
  },
})
```

### 缓存策略

```typescript
import { Redis } from 'ioredis'

const redis = new Redis(process.env.REDIS_URL)

// 缓存查询结果
export async function getCachedCompanies(key: string) {
  const cached = await redis.get(key)
  if (cached) {
    return JSON.parse(cached)
  }

  const companies = await prisma.company.findMany()
  await redis.setex(key, 300, JSON.stringify(companies)) // 缓存5分钟

  return companies
}
```

---

遵循这些指南将确保API的一致性、可维护性和高质量。记住：好的API设计是用户体验的基础。
