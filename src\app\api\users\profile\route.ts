import { auth } from '@/lib/auth'
import { getUserProfileSafely } from '@/lib/db-utils'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 用户个人信息更新的验证模式
const updateProfileSchema = z.object({
  name: z
    .string()
    .min(1, '姓名不能为空')
    .max(100, '姓名最多100个字符')
    .optional(),
  bio: z.string().max(500, '个人简介最多500个字符').optional(),
  avatar: z.string().url('头像必须是有效的URL').optional(),
  company: z.string().max(200, '公司名称最多200个字符').optional(),
  position: z.string().max(100, '职位最多100个字符').optional(),
  industry: z.string().max(100, '行业最多100个字符').optional(),
  education: z.string().max(200, '教育背景最多200个字符').optional(),
  skills: z.array(z.string()).optional(),
  experience: z
    .number()
    .int()
    .min(0, '工作年限不能为负数')
    .max(50, '工作年限不能超过50年')
    .optional(),
})

/**
 * 获取当前用户的个人信息
 * GET /api/users/profile
 *
 * 需要用户登录
 */
export async function GET() {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再获取个人信息',
          },
        },
        { status: 401 }
      )
    }

    // 使用安全的数据库操作获取用户资料
    const userProfile = await getUserProfileSafely(session.user.id)

    if (!userProfile) {
      return NextResponse.json(
        {
          success: false,
          message: '获取用户信息失败',
          error: {
            code: 'DATABASE_ERROR',
            message: '数据库连接异常，请稍后重试',
          },
        },
        { status: 500 }
      )
    }

    // 手动序列化BigInt字段
    const serializedProfile = JSON.parse(JSON.stringify(userProfile, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    ))

    return NextResponse.json({
      success: true,
      message: '获取个人信息成功',
      data: serializedProfile,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取个人信息失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取个人信息失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新当前用户的个人信息
 * PATCH /api/users/profile
 *
 * 需要用户登录
 */
export async function PATCH(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再更新个人信息',
          },
        },
        { status: 401 }
      )
    }

    const body = await request.json()

    // 验证输入数据
    const validatedData = updateProfileSchema.parse(body)

    // 检查邮箱是否已被其他用户使用（如果要更新邮箱）
    if (validatedData.name) {
      const existingUser = await prisma.user.findFirst({
        where: {
          name: validatedData.name,
          id: { not: session.user.id }, // 排除当前用户
        },
      })

      if (existingUser) {
        return NextResponse.json(
          {
            success: false,
            message: '姓名已被使用',
            error: {
              code: 'NAME_TAKEN',
              message: '该姓名已被其他用户使用，请选择其他姓名',
            },
          },
          { status: 409 }
        )
      }
    }

    // 过滤掉undefined的字段
    const updateData = Object.fromEntries(
      Object.entries(validatedData).filter(([, value]) => value !== undefined)
    )

    // 如果没有要更新的数据
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '没有提供要更新的数据',
          error: {
            code: 'NO_UPDATE_DATA',
            message: '请提供至少一个要更新的字段',
          },
        },
        { status: 400 }
      )
    }

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        email: true,
        bio: true,
        avatar: true,
        company: true,
        position: true,
        industry: true,
        education: true,
        experience: true,
        skills: true,
        updatedAt: true,
      },
    })

    // 异步更新用户资料完整度（不阻塞响应）
    const { updateUserProfileCompleteness } = await import('@/lib/update-profile-completeness')
    updateUserProfileCompleteness(session.user.id).catch(error => {
      console.error('更新用户资料完整度失败:', error)
    })

    return NextResponse.json({
      success: true,
      message: '个人信息更新成功',
      data: updatedUser,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('更新个人信息失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新个人信息失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
