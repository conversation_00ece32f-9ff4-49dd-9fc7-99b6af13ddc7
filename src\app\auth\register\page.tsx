'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Chrome, Eye, EyeOff, Lock, Mail, User } from 'lucide-react'
import { signIn } from 'next-auth/react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

/**
 * 注册页面
 * 支持邮箱注册和 Google OAuth 注册
 * 提供完整的表单验证和用户体验优化
 */
export default function RegisterPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [error, setError] = useState('')
  const [agreedToTerms, setAgreedToTerms] = useState(false)

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  })

  /**
   * 验证表单
   */
  const validateForm = () => {
    if (!formData.name || formData.name.length < 2) {
      setError('用户名至少需要2个字符')
      return false
    }

    if (!formData.email || !/\S+@\S+\.\S+/.test(formData.email)) {
      setError('请输入有效的邮箱地址')
      return false
    }

    if (!formData.password || formData.password.length < 6) {
      setError('密码至少需要6个字符')
      return false
    }

    if (formData.password !== formData.confirmPassword) {
      setError('两次输入的密码不一致')
      return false
    }

    if (!agreedToTerms) {
      setError('请同意服务条款和隐私政策')
      return false
    }

    return true
  }

  /**
   * 处理邮箱注册
   */
  const handleEmailRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // 调用注册 API
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        // 根据错误代码显示不同的提示信息
        if (data.error?.code === 'EMAIL_HAS_GOOGLE_ACCOUNT') {
          setError('此邮箱已关联Google账号，请使用Google登录')
        } else if (data.error?.code === 'EMAIL_EXISTS') {
          setError('此邮箱已注册，请直接登录')
        } else if (data.error?.code === 'VALIDATION_ERROR') {
          setError(data.error.details?.[0]?.message || '输入数据验证失败')
        } else {
          setError(data.error?.message || '注册失败，请稍后重试')
        }
        return
      }

      // 注册成功，自动登录
      const signInResult = await signIn('credentials', {
        email: formData.email,
        password: formData.password,
        redirect: false,
      })

      if (signInResult?.ok) {
        router.push('/')
        router.refresh()
      } else {
        // 注册成功但登录失败，跳转到登录页
        router.push('/auth/login')
      }
    } catch (error) {
      console.error('注册失败:', error)
      setError('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 处理 Google 注册
   * 使用与登录相同的 signIn 方法，NextAuth 会自动处理新用户创建
   */
  const handleGoogleRegister = async () => {
    setIsGoogleLoading(true)
    setError('')

    try {
      // 使用 NextAuth.js signIn 方法，会自动重定向到Google
      // 如果是新用户，NextAuth 会自动创建账号
      await signIn('google', { callbackUrl: '/' })
    } catch (error) {
      console.error('Google注册失败:', error)
      setError('Google注册失败，请稍后重试')
      setIsGoogleLoading(false)
    }
  }

  /**
   * 处理输入变化
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo 和标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">WorkMates</h1>
          <p className="mt-2 text-gray-600">加入职场社区，开启职业新篇章</p>
        </div>

        {/* 注册表单卡片 */}
        <Card className="shadow-lg border-0">
          <CardHeader className="space-y-1 pb-6">
            <CardTitle className="text-2xl font-semibold">创建账户</CardTitle>
            <CardDescription className="text-gray-600">
              选择注册方式开始使用 WorkMates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 错误提示 */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Google 注册按钮 */}
            <Button 
              onClick={handleGoogleRegister}
              variant="outline" 
              className="w-full h-12 text-gray-700 border-gray-300 hover:bg-gray-50"
              disabled={isGoogleLoading || isLoading}
            >
              <Chrome className="mr-3 h-5 w-5 text-blue-500" />
              {isGoogleLoading ? '注册中...' : '使用 Google 注册'}
            </Button>

            {/* 分隔线 */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-4 text-gray-500">或使用邮箱注册</span>
              </div>
            </div>

            {/* 邮箱注册表单 */}
            <form onSubmit={handleEmailRegister} className="space-y-4">
              {/* 用户名输入 */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-gray-700 font-medium">用户名</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    placeholder="请输入用户名"
                    className="pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              {/* 邮箱输入 */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-700 font-medium">邮箱地址</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              {/* 密码输入 */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-700 font-medium">密码</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="至少6个字符"
                    className="pl-10 pr-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* 确认密码输入 */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-gray-700 font-medium">确认密码</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="再次输入密码"
                    className="pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              {/* 服务条款 */}
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="terms"
                  checked={agreedToTerms}
                  onCheckedChange={checked =>
                    setAgreedToTerms(checked as boolean)
                  }
                  className="mt-1"
                />
                <Label
                  htmlFor="terms"
                  className="text-sm leading-relaxed cursor-pointer text-gray-700"
                >
                  我已阅读并同意
                  <Link
                    href="/terms"
                    className="text-blue-600 hover:text-blue-800 hover:underline mx-1"
                  >
                    服务条款
                  </Link>
                  和
                  <Link
                    href="/privacy"
                    className="text-blue-600 hover:text-blue-800 hover:underline mx-1"
                  >
                    隐私政策
                  </Link>
                </Label>
              </div>

              {/* 注册按钮 */}
              <Button
                type="submit"
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium"
                disabled={isLoading || isGoogleLoading || !agreedToTerms}
              >
                {isLoading ? '注册中...' : '创建账户'}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4 pt-6">
            {/* 登录链接 */}
            <p className="text-center text-sm text-gray-600">
              已有账户？
              <Link
                href="/auth/login"
                className="text-blue-600 hover:text-blue-800 hover:underline ml-1 font-medium"
              >
                立即登录
              </Link>
            </p>
          </CardFooter>
        </Card>

        {/* 附加信息 */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            注册即表示您同意我们的用户协议和数据处理政策
          </p>
        </div>
      </div>
    </div>
  )
}
