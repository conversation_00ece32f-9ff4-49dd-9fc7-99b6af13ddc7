import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import {
    Building2,
    Clock,
    DollarSign,
    Flag,
    Heart,
    MapPin,
    Share2,
    Star,
    TrendingUp,
    Users,
} from 'lucide-react'
import Link from 'next/link'

/**
 * 企业详情页面
 * 展示企业的详细信息、评分、薪资、面经等
 */
export default async function CompanyDetailPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // 在 Next.js 15 中，params 是一个 Promise
  const { id } = await params

  // 从API获取企业信息
  let company = null
  let error = null

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/companies/${id}`)
    const data = await response.json()
    
    if (data.success) {
      company = data.data
    } else {
      error = data.message
    }
  } catch {
    error = '加载企业信息失败'
  }

  // 如果没有获取到数据，显示错误页面
  if (!company) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="text-center py-12">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">企业信息加载失败</h1>
            <p className="text-gray-600 mb-6">{error || '未找到该企业信息'}</p>
            <Button asChild>
              <Link href="/companies">返回企业列表</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 企业头部信息 */}
      <Card className="mb-8">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-6">
              {/* 企业Logo */}
              <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                <span className="text-sm text-gray-400">Logo</span>
              </div>

              {/* 企业基本信息 */}
              <div>
                <CardTitle className="text-3xl mb-2">{company.name}</CardTitle>
                <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                  <span className="flex items-center gap-1">
                    <Building2 className="h-4 w-4" />
                    {company.industry || '未知行业'}
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    {company.size === 'STARTUP' ? '创业公司' : 
                     company.size === 'SMALL' ? '小型公司' :
                     company.size === 'MEDIUM' ? '中型公司' :
                     company.size === 'LARGE' ? '大型公司' :
                     company.size === 'ENTERPRISE' ? '企业级' : '未知规模'}
                  </span>
                  <span className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    {company.location || '未知位置'}
                  </span>
                </div>
                <CardDescription>{company.description || '暂无企业描述'}</CardDescription>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <Button variant="outline" size="icon">
                <Heart className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Share2 className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Flag className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 总体评分 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center">
                <Star className="h-8 w-8 fill-yellow-400 text-yellow-400" />
                <span className="ml-2 text-3xl font-bold">
                  {company.averageRating ? Number(company.averageRating).toFixed(1) : '暂无评分'}
                </span>
                <span className="ml-2 text-gray-600">/ 5.0</span>
              </div>
              <div className="text-sm text-gray-600">
                基于 {company.totalReviews || 0} 条评价
              </div>
            </div>
            <Button>写评价</Button>
          </div>
        </CardContent>
      </Card>

      {/* 内容标签页 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="reviews">评价</TabsTrigger>
          <TabsTrigger value="salaries">薪资</TabsTrigger>
          <TabsTrigger value="interviews">面经</TabsTrigger>
        </TabsList>

        {/* 概览标签页 */}
        <TabsContent value="overview" className="space-y-6">
          <CompanyOverview 
            company={{
              rating: {
                salary: company.averageRating || 0,
                workLife: company.averageRating || 0,
                culture: company.averageRating || 0,
                management: company.averageRating || 0,
                growth: company.averageRating || 0,
              },
              reviewCount: company.totalReviews || 0,
              salaryCount: company.totalSalaries || 0,
              interviewCount: company.totalInterviews || 0,
            }} 
          />
        </TabsContent>

        {/* 评价标签页 */}
        <TabsContent value="reviews" className="space-y-6">
          <CompanyReviews />
        </TabsContent>

        {/* 薪资标签页 */}
        <TabsContent value="salaries" className="space-y-6">
          <CompanySalaries />
        </TabsContent>

        {/* 面经标签页 */}
        <TabsContent value="interviews" className="space-y-6">
          <CompanyInterviews />
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 企业概览组件
 */
function CompanyOverview({
  company,
}: {
  company: {
    rating: {
      salary: number
      workLife: number
      culture: number
      management: number
      growth: number
    }
    reviewCount: number
    salaryCount: number
    interviewCount: number
  }
}) {
  const ratingItems = [
    {
      key: 'salary',
      label: '薪资福利',
      value: company.rating.salary,
      icon: DollarSign,
    },
    {
      key: 'workLife',
      label: '工作生活',
      value: company.rating.workLife,
      icon: Clock,
    },
    {
      key: 'culture',
      label: '企业文化',
      value: company.rating.culture,
      icon: Heart,
    },
    {
      key: 'management',
      label: '管理制度',
      value: company.rating.management,
      icon: Users,
    },
    {
      key: 'growth',
      label: '发展前景',
      value: company.rating.growth,
      icon: TrendingUp,
    },
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* 评分详情 */}
      <Card>
        <CardHeader>
          <CardTitle>评分详情</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {ratingItems.map(item => (
            <div key={item.key} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="flex items-center gap-2 text-sm">
                  <item.icon className="h-4 w-4 text-gray-400" />
                  {item.label}
                </span>
                <span className="text-sm font-semibold">{item.value}</span>
              </div>
              <Progress value={item.value * 20} className="h-2" />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 数据统计 */}
      <Card>
        <CardHeader>
          <CardTitle>数据统计</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold">{company.reviewCount}</p>
              <p className="text-sm text-gray-600">条评价</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{company.salaryCount}</p>
              <p className="text-sm text-gray-600">薪资数据</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{company.interviewCount}</p>
              <p className="text-sm text-gray-600">面试经验</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * 企业评价列表
 */
function CompanyReviews() {
  const reviews = [
    {
      id: 1,
      author: '匿名员工',
      position: '高级工程师',
      rating: 4,
      date: '2024-03-15',
      pros: '技术氛围好，同事都很优秀，能学到很多东西',
      cons: '加班比较多，工作压力大',
      content: '在阿里工作了3年，整体感觉还不错...',
    },
    {
      id: 2,
      author: '前员工',
      position: '产品经理',
      rating: 3.5,
      date: '2024-02-20',
      pros: '平台大，资源多，薪资待遇不错',
      cons: '内部竞争激烈，晋升困难',
      content: '阿里是一个很好的平台，但是...',
    },
  ]

  return (
    <div className="space-y-4">
      {reviews.map(review => (
        <Card key={review.id}>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-lg">{review.author}</CardTitle>
                <CardDescription>
                  {review.position} ·{' '}
                  {new Date(review.date).toLocaleDateString()}
                </CardDescription>
              </div>
              <div className="flex items-center">
                <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                <span className="ml-1 font-semibold">{review.rating}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="font-semibold text-green-600 mb-1">优点</p>
              <p className="text-sm">{review.pros}</p>
            </div>
            <div>
              <p className="font-semibold text-red-600 mb-1">缺点</p>
              <p className="text-sm">{review.cons}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">{review.content}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * 企业薪资信息
 */
function CompanySalaries() {
  const salaries = [
    {
      position: '高级工程师',
      avgSalary: '30-45K',
      samples: 125,
      range: { min: 25, max: 50 },
    },
    {
      position: '产品经理',
      avgSalary: '25-40K',
      samples: 89,
      range: { min: 20, max: 45 },
    },
    {
      position: '初级工程师',
      avgSalary: '15-25K',
      samples: 156,
      range: { min: 12, max: 28 },
    },
  ]

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>薪资水平</CardTitle>
          <CardDescription>基于用户匿名分享的真实数据</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {salaries.map(salary => (
              <div key={salary.position} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold">{salary.position}</p>
                    <p className="text-sm text-gray-600">
                      {salary.samples} 个样本
                    </p>
                  </div>
                  <Badge variant="secondary" className="text-lg">
                    {salary.avgSalary}
                  </Badge>
                </div>
                <div className="text-xs text-gray-600">
                  薪资范围：{salary.range.min}K - {salary.range.max}K
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * 企业面试经验
 */
function CompanyInterviews() {
  const interviews = [
    {
      id: 1,
      position: '前端工程师',
      result: '通过',
      difficulty: 4,
      date: '2024-03-10',
      process: '一共4轮，包括技术面、项目面、HR面',
      questions: ['React Hooks 原理', '性能优化方案', '项目难点'],
    },
    {
      id: 2,
      position: '后端工程师',
      result: '未通过',
      difficulty: 5,
      date: '2024-02-28',
      process: '3轮技术面 + 1轮HR面',
      questions: ['分布式系统设计', '数据库优化', '算法题'],
    },
  ]

  return (
    <div className="space-y-4">
      {interviews.map(interview => (
        <Card key={interview.id}>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-lg">{interview.position}</CardTitle>
                <CardDescription>
                  {new Date(interview.date).toLocaleDateString()}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={
                    interview.result === '通过' ? 'default' : 'secondary'
                  }
                >
                  {interview.result}
                </Badge>
                <Badge variant="outline">难度 {interview.difficulty}/5</Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <p className="font-semibold mb-1">面试流程</p>
              <p className="text-sm text-gray-600">{interview.process}</p>
            </div>
            <div>
              <p className="font-semibold mb-1">面试问题</p>
              <ul className="list-disc list-inside text-sm text-gray-600">
                {interview.questions.map((q, i) => (
                  <li key={i}>{q}</li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
