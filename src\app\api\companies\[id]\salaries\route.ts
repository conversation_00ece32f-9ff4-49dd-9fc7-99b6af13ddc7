import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 创建薪资的验证模式
const createSalarySchema = z.object({
  position: z.string().min(1, '职位名称不能为空').max(100),
  level: z.string().max(50).optional(),
  workLocation: z.string().max(100).optional(),
  workType: z.string().max(50).optional(),
  experience: z.number().int().min(0).max(50).optional(),
  baseSalary: z.number().positive('基础薪资必须大于0').optional(),
  bonus: z.number().min(0).optional(),
  stockOptions: z.number().min(0).optional(),
  benefits: z.number().min(0).optional(),
  totalSalary: z.number().positive('总薪资必须大于0'),
  currency: z.string().max(10).default('CNY'),
  salaryYear: z.number().int().min(2000).max(new Date().getFullYear()),
  notes: z.string().optional(),
  tags: z.array(z.string()).default([]),
  isAnonymous: z.boolean().default(true),
})

/**
 * 获取公司薪资列表
 * GET /api/companies/[id]/salaries
 *
 * 查询参数:
 * - page: 页码 (默认 1)
 * - limit: 每页数量 (默认 20)
 * - position: 职位筛选
 * - level: 级别筛选
 * - experience: 经验范围筛选 (格式: "0-3", "3-5", "5+")
 * - sort: 排序字段 (totalSalary, createdAt, experience)
 * - order: 排序方向 (asc, desc)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const position = url.searchParams.get('position')
    const level = url.searchParams.get('level')
    const experience = url.searchParams.get('experience')
    const sort = url.searchParams.get('sort') || 'createdAt'
    const order = url.searchParams.get('order') || 'desc'

    // 验证公司是否存在且激活
    const company = await prisma.company.findFirst({
      where: { id: companyId, isActive: true },
    })

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'COMPANY_NOT_FOUND',
            message: '指定的公司不存在或已被删除',
          },
        },
        { status: 404 }
      )
    }

    // 构建查询条件
    const where: any = {
      companyId,
      isActive: true,
    }

    if (position) {
      where.position = { contains: position, mode: 'insensitive' }
    }

    if (level) {
      where.level = level
    }

    // 经验筛选
    if (experience) {
      if (experience === '0-3') {
        where.experience = { gte: 0, lte: 3 }
      } else if (experience === '3-5') {
        where.experience = { gte: 3, lte: 5 }
      } else if (experience === '5-10') {
        where.experience = { gte: 5, lte: 10 }
      } else if (experience === '10+') {
        where.experience = { gte: 10 }
      }
    }

    // 构建排序条件
    const orderBy: any = {}
    if (sort === 'totalSalary') {
      orderBy.totalSalary = order
    } else if (sort === 'experience') {
      orderBy.experience = order
    } else {
      orderBy.createdAt = order
    }

    // 执行查询
    const [salaries, total, stats] = await Promise.all([
      prisma.salary.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        select: {
          id: true,
          position: true,
          level: true,
          workLocation: true,
          workType: true,
          experience: true,
          baseSalary: true,
          bonus: true,
          totalSalary: true,
          currency: true,
          notes: true,
          tags: true,
          isVerified: true,
          createdAt: true,
        },
        orderBy,
      }),
      prisma.salary.count({ where }),
      // 薪资统计
      prisma.salary.aggregate({
        where,
        _avg: {
          totalSalary: true,
          baseSalary: true,
          bonus: true,
          experience: true,
        },
        _min: {
          totalSalary: true,
        },
        _max: {
          totalSalary: true,
        },
      }),
    ])

    return NextResponse.json({
      success: true,
      message: '获取薪资列表成功',
      data: salaries,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        statistics: {
          averageTotal: stats._avg.totalSalary
            ? Number(stats._avg.totalSalary)
            : 0,
          averageBase: stats._avg.baseSalary
            ? Number(stats._avg.baseSalary)
            : 0,
          averageBonus: stats._avg.bonus ? Number(stats._avg.bonus) : 0,
          averageExperience: stats._avg.experience
            ? Number(stats._avg.experience)
            : 0,
          minSalary: stats._min.totalSalary
            ? Number(stats._min.totalSalary)
            : 0,
          maxSalary: stats._max.totalSalary
            ? Number(stats._max.totalSalary)
            : 0,
        },
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取薪资列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取薪资列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 提交薪资数据
 * POST /api/companies/[id]/salaries
 *
 * 需要用户登录
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再提交薪资信息',
          },
        },
        { status: 401 }
      )
    }

    const { id: companyId } = await params
    const body = await request.json()

    // 验证输入数据
    const validatedData = createSalarySchema.parse(body)

    // 验证公司是否存在且激活
    const company = await prisma.company.findFirst({
      where: { id: companyId, isActive: true },
    })

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'COMPANY_NOT_FOUND',
            message: '指定的公司不存在或已被删除',
          },
        },
        { status: 404 }
      )
    }

    // 检查用户是否已为该公司提交过薪资 (可选限制)
    // const existingSalary = await prisma.salary.findFirst({
    //   where: { authorId: session.user.id, companyId, isActive: true }
    // })
    // if (existingSalary) {
    //   return NextResponse.json({
    //     success: false,
    //     message: '您已为该公司提交过薪资信息',
    //     error: { code: 'DUPLICATE_SALARY', message: '每个用户只能为每家公司提交一次薪资信息' }
    //   }, { status: 409 })
    // }

    // 创建薪资记录
    const salary = await prisma.salary.create({
      data: {
        ...validatedData,
        authorId: session.user.id,
        companyId,
        isVerified: false, // 新提交的薪资默认未验证
      },
      select: {
        id: true,
        position: true,
        level: true,
        totalSalary: true,
        isAnonymous: true,
        createdAt: true,
      },
    })

    // 更新公司的薪资统计
    await updateCompanySalaryStats(companyId)

    return NextResponse.json(
      {
        success: true,
        message: '提交薪资成功',
        data: salary,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('提交薪资失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '提交薪资失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新公司薪资统计
 * 重新计算公司的薪资相关统计数据
 */
async function updateCompanySalaryStats(companyId: string) {
  try {
    const stats = await prisma.salary.aggregate({
      where: {
        companyId,
      },
      _count: true,
      _avg: {
        totalSalary: true,
      },
    })

    await prisma.company.update({
      where: { id: companyId },
      data: {
        totalSalaries: stats._count,
      },
    })
  } catch (error) {
    console.error('更新公司薪资统计失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
