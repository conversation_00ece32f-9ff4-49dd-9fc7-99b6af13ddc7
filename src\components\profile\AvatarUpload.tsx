'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Upload, Loader2, CheckCircle, XCircle, Camera } from 'lucide-react'
import { useSession } from 'next-auth/react'

interface AvatarUploadProps {
  currentAvatar?: string
  userName?: string
  onUploadSuccess?: (newAvatarUrl: string) => void
  className?: string
}

interface UploadResult {
  success: boolean
  message: string
  data?: {
    user: {
      id: string
      name: string
      email: string
      avatar: string
    }
    upload: {
      url: string
      key: string
      fileName: string
    }
  }
  error?: {
    code: string
    message: string
  }
}

export function AvatarUpload({ 
  currentAvatar, 
  userName, 
  onUploadSuccess,
  className = ""
}: AvatarUploadProps) {
  const { data: session, update } = useSession()
  const [uploading, setUploading] = useState(false)
  const [result, setResult] = useState<UploadResult | null>(null)
  const [preview, setPreview] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setResult(null)

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setResult({
        success: false,
        message: '不支持的文件类型',
        error: {
          code: 'INVALID_FILE_TYPE',
          message: '仅支持 JPEG、PNG、WebP 格式'
        }
      })
      return
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setResult({
        success: false,
        message: '文件大小超出限制',
        error: {
          code: 'FILE_TOO_LARGE',
          message: '文件大小不能超过 5MB'
        }
      })
      return
    }

    // 生成预览
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)

    // 自动上传
    handleUpload(file)
  }

  const handleUpload = async (file: File) => {
    setUploading(true)
    setResult(null)

    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch('/api/upload/avatar-r2', {
        method: 'POST',
        body: formData,
      })

      const data: UploadResult = await response.json()
      setResult(data)

      if (data.success && data.data) {
        // 更新 session 中的头像
        await update({
          ...session,
          user: {
            ...session?.user,
            image: data.data.upload.url
          }
        })

        // 调用成功回调
        onUploadSuccess?.(data.data.upload.url)
        
        // 清除预览
        setPreview(null)
        
        // 重置文件输入
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
      }
    } catch (error) {
      console.error('上传失败:', error)
      setResult({
        success: false,
        message: '网络错误',
        error: {
          code: 'NETWORK_ERROR',
          message: error instanceof Error ? error.message : '未知网络错误'
        }
      })
    } finally {
      setUploading(false)
    }
  }

  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  const displayAvatar = preview || currentAvatar || session?.user?.image
  const displayName = userName || session?.user?.name || session?.user?.email || '?'

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 头像显示和上传按钮 */}
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <Avatar className="h-32 w-32">
            <AvatarImage src={displayAvatar} alt={displayName} />
            <AvatarFallback className="text-2xl">
              {displayName[0].toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          {/* 上传按钮覆盖层 */}
          <button
            onClick={triggerFileSelect}
            disabled={uploading}
            className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity duration-200 disabled:cursor-not-allowed"
          >
            {uploading ? (
              <Loader2 className="h-8 w-8 text-white animate-spin" />
            ) : (
              <Camera className="h-8 w-8 text-white" />
            )}
          </button>
        </div>

        {/* 上传按钮 */}
        <Button 
          variant="outline" 
          onClick={triggerFileSelect}
          disabled={uploading}
          className="w-full max-w-xs"
        >
          {uploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              上传中...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              {displayAvatar ? '更换头像' : '上传头像'}
            </>
          )}
        </Button>

        {/* 文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/webp"
          onChange={handleFileSelect}
          className="hidden"
        />

        {/* 提示文字 */}
        <p className="text-xs text-gray-500 text-center">
          支持 JPEG、PNG、WebP 格式<br />
          文件大小不超过 5MB
        </p>
      </div>

      {/* 上传结果 */}
      {result && (
        <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          <div className="flex items-start gap-2">
            {result.success ? (
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
            ) : (
              <XCircle className="h-4 w-4 text-red-600 mt-0.5" />
            )}
            <div className="flex-1">
              <AlertDescription>
                <div className="font-medium">
                  {result.success ? '头像更新成功！' : '上传失败'}
                </div>
                <div className="text-sm mt-1">{result.message}</div>
                
                {!result.success && result.error && (
                  <div className="mt-2 text-xs text-red-600">
                    {result.error.message}
                  </div>
                )}
              </AlertDescription>
            </div>
          </div>
        </Alert>
      )}
    </div>
  )
}
