// test-db-connection.js
const { PrismaClient } = require('@prisma/client')
require('dotenv').config({ path: '.env.local' })

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function testConnection() {
  console.log('开始测试数据库连接...')
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? '已设置' : '未设置')
  console.log('DIRECT_URL:', process.env.DIRECT_URL ? '已设置' : '未设置')
  
  try {
    // 测试基本连接
    console.log('\n1. 测试基本连接...')
    await prisma.$connect()
    console.log('✅ 数据库连接成功')
    
    // 测试查询
    console.log('\n2. 测试简单查询...')
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ 查询测试成功:', result)
    
    // 测试表是否存在
    console.log('\n3. 检查表结构...')
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `
    console.log('✅ 数据库表列表:')
    tables.forEach(table => console.log(`  - ${table.table_name}`))
    
    // 测试用户表
    console.log('\n4. 测试用户表...')
    const userCount = await prisma.user.count()
    console.log(`✅ 用户表记录数: ${userCount}`)
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message)
    console.error('错误详情:', error)
  } finally {
    await prisma.$disconnect()
    console.log('\n数据库连接已关闭')
  }
}

testConnection()
