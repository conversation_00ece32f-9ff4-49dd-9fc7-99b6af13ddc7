import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

/**
 * 获取用户通知设置
 * GET /api/notifications/settings
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    // 获取用户通知设置，如果不存在则创建默认设置
    let notificationSettings = await prisma.notificationSetting.findUnique({
      where: { userId: session.user.id },
    })

    if (!notificationSettings) {
      // 创建默认通知设置
      notificationSettings = await prisma.notificationSetting.create({
        data: {
          userId: session.user.id,
        },
      })
    }

    return NextResponse.json({
      success: true,
      data: notificationSettings,
    })
  } catch (error) {
    console.error('获取通知设置失败:', error)
    return NextResponse.json(
      { success: false, message: '获取通知设置失败' },
      { status: 500 }
    )
  }
}

/**
 * 更新用户通知设置
 * PUT /api/notifications/settings
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    const body = await request.json()
    
    // 验证时间格式（如果提供了免打扰时间）
    if (body.quietHoursStart && !isValidTimeFormat(body.quietHoursStart)) {
      return NextResponse.json(
        { success: false, message: '免打扰开始时间格式无效' },
        { status: 400 }
      )
    }

    if (body.quietHoursEnd && !isValidTimeFormat(body.quietHoursEnd)) {
      return NextResponse.json(
        { success: false, message: '免打扰结束时间格式无效' },
        { status: 400 }
      )
    }

    // 更新或创建通知设置
    const notificationSettings = await prisma.notificationSetting.upsert({
      where: { userId: session.user.id },
      update: {
        // 社交通知设置
        enableLike: body.enableLike,
        enableComment: body.enableComment,
        enableReply: body.enableReply,
        enableFollow: body.enableFollow,
        enableMention: body.enableMention,
        
        // 工作相关通知设置
        enableJobInvitation: body.enableJobInvitation,
        enableInterviewInvite: body.enableInterviewInvite,
        enableSalaryRequest: body.enableSalaryRequest,
        enableCompanyUpdate: body.enableCompanyUpdate,
        
        // 内容通知设置
        enablePostFeatured: body.enablePostFeatured,
        enablePostApproved: body.enablePostApproved,
        enablePostRejected: body.enablePostRejected,
        enableContentReported: body.enableContentReported,
        
        // 系统通知设置
        enableSystemUpdate: body.enableSystemUpdate,
        enableSecurityAlert: body.enableSecurityAlert,
        enableAccountVerified: body.enableAccountVerified,
        enablePolicyUpdate: body.enablePolicyUpdate,
        enableMaintenance: body.enableMaintenance,
        
        // 私信通知设置
        enablePrivateMessage: body.enablePrivateMessage,
        enableGroupMessage: body.enableGroupMessage,
        
        // 通知渠道设置
        enableWebNotification: body.enableWebNotification,
        enableEmailNotification: body.enableEmailNotification,
        enablePushNotification: body.enablePushNotification,
        
        // 通知时间设置
        quietHoursStart: body.quietHoursStart,
        quietHoursEnd: body.quietHoursEnd,
        enableQuietHours: body.enableQuietHours,
      },
      create: {
        userId: session.user.id,
        // 社交通知设置
        enableLike: body.enableLike ?? true,
        enableComment: body.enableComment ?? true,
        enableReply: body.enableReply ?? true,
        enableFollow: body.enableFollow ?? true,
        enableMention: body.enableMention ?? true,
        
        // 工作相关通知设置
        enableJobInvitation: body.enableJobInvitation ?? true,
        enableInterviewInvite: body.enableInterviewInvite ?? true,
        enableSalaryRequest: body.enableSalaryRequest ?? true,
        enableCompanyUpdate: body.enableCompanyUpdate ?? false,
        
        // 内容通知设置
        enablePostFeatured: body.enablePostFeatured ?? true,
        enablePostApproved: body.enablePostApproved ?? true,
        enablePostRejected: body.enablePostRejected ?? true,
        enableContentReported: body.enableContentReported ?? true,
        
        // 系统通知设置
        enableSystemUpdate: body.enableSystemUpdate ?? true,
        enableSecurityAlert: body.enableSecurityAlert ?? true,
        enableAccountVerified: body.enableAccountVerified ?? true,
        enablePolicyUpdate: body.enablePolicyUpdate ?? false,
        enableMaintenance: body.enableMaintenance ?? false,
        
        // 私信通知设置
        enablePrivateMessage: body.enablePrivateMessage ?? true,
        enableGroupMessage: body.enableGroupMessage ?? true,
        
        // 通知渠道设置
        enableWebNotification: body.enableWebNotification ?? true,
        enableEmailNotification: body.enableEmailNotification ?? false,
        enablePushNotification: body.enablePushNotification ?? false,
        
        // 通知时间设置
        quietHoursStart: body.quietHoursStart,
        quietHoursEnd: body.quietHoursEnd,
        enableQuietHours: body.enableQuietHours ?? false,
      },
    })

    return NextResponse.json({
      success: true,
      message: '通知设置更新成功',
      data: notificationSettings,
    })
  } catch (error) {
    console.error('更新通知设置失败:', error)
    return NextResponse.json(
      { success: false, message: '更新通知设置失败' },
      { status: 500 }
    )
  }
}

/**
 * 验证时间格式 (HH:MM)
 */
function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(time)
}

/**
 * 重置通知设置为默认值
 * POST /api/notifications/settings/reset
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    // 删除现有设置，让系统使用默认值
    await prisma.notificationSetting.deleteMany({
      where: { userId: session.user.id },
    })

    // 创建新的默认设置
    const notificationSettings = await prisma.notificationSetting.create({
      data: {
        userId: session.user.id,
      },
    })

    return NextResponse.json({
      success: true,
      message: '通知设置已重置为默认值',
      data: notificationSettings,
    })
  } catch (error) {
    console.error('重置通知设置失败:', error)
    return NextResponse.json(
      { success: false, message: '重置通知设置失败' },
      { status: 500 }
    )
  }
}
