import { prisma } from './prisma'
import { calculateProfileCompleteness } from './profile-utils'

/**
 * 更新用户资料完整度
 * @param userId 用户ID
 */
export async function updateUserProfileCompleteness(userId: string): Promise<void> {
  try {
    // 获取用户当前资料
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        name: true,
        bio: true,
        avatar: true,
        company: true,
        position: true,
        industry: true,
        education: true,
        experience: true,
        skills: true,
        phone: true,
      },
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    // 计算完整度
    const completeness = calculateProfileCompleteness(user)

    // 更新数据库
    await prisma.user.update({
      where: { id: userId },
      data: {
        profileCompleteness: completeness,
        lastProfileUpdate: new Date(),
      },
    })

    console.log(`用户 ${userId} 资料完整度已更新为 ${completeness}%`)
  } catch (error) {
    console.error('更新用户资料完整度失败:', error)
    throw error
  }
}

/**
 * 批量更新所有用户的资料完整度
 */
export async function updateAllUsersProfileCompleteness(): Promise<void> {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        bio: true,
        avatar: true,
        company: true,
        position: true,
        industry: true,
        education: true,
        experience: true,
        skills: true,
        phone: true,
      },
    })

    console.log(`开始更新 ${users.length} 个用户的资料完整度...`)

    for (const user of users) {
      const completeness = calculateProfileCompleteness(user)
      
      await prisma.user.update({
        where: { id: user.id },
        data: {
          profileCompleteness: completeness,
          lastProfileUpdate: new Date(),
        },
      })
    }

    console.log('所有用户资料完整度更新完成')
  } catch (error) {
    console.error('批量更新用户资料完整度失败:', error)
    throw error
  }
}

/**
 * 更新用户存储空间使用量
 * @param userId 用户ID
 * @param sizeChange 存储空间变化量（字节，可为负数）
 */
export async function updateUserStorageUsage(userId: string, sizeChange: number): Promise<void> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { storageUsed: true, storageLimit: true },
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const currentUsed = Number(user.storageUsed || 0)
    const newUsed = Math.max(0, currentUsed + sizeChange)
    const limit = Number(user.storageLimit || 104857600) // 默认100MB

    if (newUsed > limit) {
      throw new Error('存储空间不足')
    }

    await prisma.user.update({
      where: { id: userId },
      data: { storageUsed: newUsed },
    })

    console.log(`用户 ${userId} 存储使用量已更新: ${currentUsed} -> ${newUsed} 字节`)
  } catch (error) {
    console.error('更新用户存储使用量失败:', error)
    throw error
  }
}

/**
 * 检查用户是否可以上传指定大小的文件
 * @param userId 用户ID
 * @param fileSize 文件大小（字节）
 * @returns 是否可以上传
 */
export async function canUserUploadFile(userId: string, fileSize: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { storageUsed: true, storageLimit: true },
    })

    if (!user) {
      return false
    }

    const currentUsed = Number(user.storageUsed || 0)
    const limit = Number(user.storageLimit || 104857600) // 默认100MB

    return (currentUsed + fileSize) <= limit
  } catch (error) {
    console.error('检查用户上传权限失败:', error)
    return false
  }
}

/**
 * 获取用户存储空间统计
 * @param userId 用户ID
 * @returns 存储空间统计信息
 */
export async function getUserStorageStats(userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { storageUsed: true, storageLimit: true },
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    const used = Number(user.storageUsed || 0)
    const limit = Number(user.storageLimit || 104857600)
    const available = limit - used
    const usagePercentage = Math.round((used / limit) * 100)

    return {
      used,
      limit,
      available,
      usagePercentage,
      canUpload: available > 0,
    }
  } catch (error) {
    console.error('获取用户存储统计失败:', error)
    throw error
  }
}
