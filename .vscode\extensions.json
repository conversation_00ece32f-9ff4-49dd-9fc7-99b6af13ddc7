{
  "recommendations": [
    // ===== 必装扩展 =====
    "esbenp.prettier-vscode", // 代码格式化
    "dbaeumer.vscode-eslint", // ESLint 支持
    "bradlc.vscode-tailwindcss", // Tailwind CSS 智能感知
    "ms-vscode.vscode-typescript-next", // TypeScript 增强支持

    // ===== 调试工具 =====
    "ms-vscode.vscode-js-debug", // JavaScript 调试器
    "ms-vscode.vscode-js-debug-companion", // 调试伴侣
    "firefox-devtools.vscode-firefox-debug", // Firefox 调试支持

    // ===== Next.js 生态 =====
    "formulahendry.auto-rename-tag", // 自动重命名标签
    "bradlc.vscode-tailwindcss", // Tailwind 支持
    "prisma.prisma", // Prisma 支持
    "ms-vscode.vscode-json", // JSON 支持

    // ===== 开发效率工具 =====
    "christian-kohler.path-intellisense", // 路径自动补全
    "christian-kohler.npm-intellisense", // npm 包智能感知
    "formulahendry.auto-close-tag", // 自动闭合标签
    "ms-vscode.vscode-typescript-next", // TypeScript 支持
    "yzhang.markdown-all-in-one", // Markdown 支持

    // ===== Git 工具 =====
    "eamodio.gitlens", // Git 增强
    "mhutchie.git-graph", // Git 图形化

    // ===== 主题和图标 =====
    "pkief.material-icon-theme", // 文件图标主题
    "github.github-vscode-theme", // GitHub 主题

    // ===== 代码质量 =====
    "streetsidesoftware.code-spell-checker", // 拼写检查
    "usernamehw.errorlens", // 错误高亮
    "ms-vscode.vscode-json", // JSON 工具

    // ===== 实用工具 =====
    "gruntfuggly.todo-tree", // TODO 树状图
    "aaron-bond.better-comments", // 更好的注释
    "ms-vscode.live-server", // 实时服务器
    "redhat.vscode-yaml", // YAML 支持

    // ===== React 开发工具 =====
    "dsznajder.es7-react-js-snippets", // React 代码片段
    "ms-vscode.vscode-react-javascript", // React JavaScript 支持

    // ===== 数据库工具 =====
    "ms-mssql.mssql", // SQL Server 支持
    "mtxr.sqltools", // SQL 工具

    // ===== 环境配置 =====
    "ms-vscode.vscode-dotenv", // .env 文件支持
    "mikestead.dotenv" // dotenv 语法高亮
  ],

  "unwantedRecommendations": [
    // 避免冲突的扩展
    "ms-vscode.vscode-typescript", // 使用 typescript-next 替代
    "hookyqr.beautify", // 使用 prettier 替代
    "vscode.typescript-language-features" // 已内置
  ]
}
