-- WorkMates 数据库视图与函数
-- 版本: 1.1
-- 最后更新: 2025-07-14
-- 此文件包含 WorkMates 数据库的所有视图和函数。

-- ================================
-- 函数
-- ================================

-- 触发器函数，用于在行修改时自动更新 `updatedAt` 时间戳。
-- 这确保了 `updatedAt` 字段始终反映记录的最后更改时间。
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 视图
-- ================================

-- 注意: 视图是基于SQL语句结果集的虚拟表。
-- 它们可用于简化复杂查询、汇总数据，并为外部工具提供稳定的API。

-- 示例视图 (请在下方添加您自己的视图):
/*
CREATE OR REPLACE VIEW public.user_profiles AS
  SELECT id, name, email, bio, position, company
  FROM users;
*/
