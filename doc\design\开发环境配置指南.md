# WorkMates 开发环境配置指南

## 📋 概述

本文档提供 WorkMates 项目的完整开发环境配置指南，包括环境要求、开发工具配置、调试设置和开发流程。特别针对 Next.js 全栈应用的调试难点提供详细的解决方案。

## 🛠️ 环境要求

### 基础要求

- **Node.js**: >= 18.17.0 (推荐使用 20.x LTS)
- **npm**: >= 9.0.0 或 **pnpm**: >= 8.0.0
- **Git**: 最新版本
- **VSCode**: 最新版本（推荐）

### 技术栈

- **前端**: Next.js 15 (App Router) + TypeScript + TailwindCSS
- **UI库**: shadcn/ui + Radix UI
- **数据库**: Supabase PostgreSQL
- **ORM**: Prisma
- **认证**: NextAuth.js
- **部署**: Vercel

## 🔧 开发工具配置

### VSCode 推荐扩展

```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "prisma.prisma",
    "dsznajder.es7-react-js-snippets",
    "usernamehw.errorlens",
    "eamodio.gitlens"
  ]
}
```

### VSCode 调试配置详解

理解 Next.js 调试的核心概念：Next.js 应用实际上运行在两个不同的 JavaScript 环境中。服务端代码运行在 Node.js 环境中，负责处理 API 路由、服务端渲染等功能。客户端代码运行在浏览器环境中，负责处理用户交互、React 组件的客户端逻辑等。这两个环境需要不同的调试配置。

创建 `.vscode/launch.json` 文件：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "🚀 Next.js: 服务端调试",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/next/dist/bin/next",
      "args": ["dev"],
      "cwd": "${workspaceFolder}",
      "skipFiles": ["<node_internals>/**"],
      "env": {
        "NODE_OPTIONS": "--inspect"
      },
      "console": "integratedTerminal",
      "runtimeExecutable": "node"
    },
    {
      "name": "🔗 Next.js: 连接到运行中的服务器",
      "type": "node",
      "request": "attach",
      "port": 9229,
      "skipFiles": ["<node_internals>/**"],
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "${workspaceFolder}"
    },
    {
      "name": "🌐 Next.js: 客户端调试 (简化版)",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/next/dist/bin/next",
      "args": ["dev"],
      "console": "integratedTerminal",
      "serverReadyAction": {
        "pattern": "- Local:.+(https?://localhost:[0-9]+)",
        "uriFormat": "%s",
        "action": "openExternally"
      }
    }
  ]
}
```

### 调试配置的使用策略

**渐进式调试方法：** 不要一开始就尝试复杂的全栈调试。推荐按照以下顺序逐步测试调试功能：

1. **首先测试服务端调试**：选择 "🚀 Next.js: 服务端调试" 配置，这个配置专门用于调试 API 路由、中间件、服务端组件等运行在 Node.js 环境中的代码。

2. **如果遇到连接问题**：使用 "🔗 Next.js: 连接到运行中的服务器" 配置。先在终端中手动启动应用：

   ```bash
   node --inspect node_modules/next/dist/bin/next dev
   ```

   然后在 VSCode 中选择连接配置。

3. **客户端代码调试**：对于标记了 `"use client"` 的组件，推荐使用浏览器的开发者工具进行调试，而不是依赖 VSCode 的调试器。

### Next.js 项目的特殊配置

确保你的 `next.config.js` 文件包含正确的开发环境配置：

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 开发环境下启用源映射，这对调试至关重要
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // 为客户端代码启用详细的源映射
      config.devtool = 'eval-source-map'
    }
    return config
  },

  // 在调试期间可以考虑禁用严格模式，因为它可能干扰调试器
  reactStrictMode: process.env.NODE_ENV !== 'development',

  // 确保错误堆栈跟踪包含源代码位置
  experimental: {
    instrumentationHook: true,
  },
}

module.exports = nextConfig
```

## 🚀 快速开始

### 1. 项目初始化

```bash
# 克隆项目
git clone [repository-url]
cd WorkMates

# 安装依赖
npm install
# 或者使用 pnpm
pnpm install
```

### 2. 环境变量配置

复制 `env.example` 并创建 `.env.local`：

```bash
cp env.example .env.local
```

配置以下环境变量：

```env
# 数据库配置
DATABASE_URL="postgresql://..."
DIRECT_URL="postgresql://..."

# NextAuth.js 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
```

### 3. 数据库设置

```bash
# 生成 Prisma 客户端
npx prisma generate

# 同步数据库结构
npx prisma db push

# 查看数据库
npx prisma studio
```

### 4. 启动开发服务器

```bash
npm run dev
# 或者
pnpm dev
```

访问 http://localhost:3000 查看应用。

## 🎯 调试指南详解

### 理解 Next.js 调试的复杂性

在传统的前端或后端应用中，调试相对简单，因为代码运行在单一的执行环境中。但是 Next.js 作为全栈框架，代码可能运行在两个完全不同的环境中，这使得调试变得复杂。

**服务端环境**：包括 API 路由（`/api` 目录下的文件）、服务端组件（没有 `"use client"` 指令的组件）、中间件（`middleware.ts`）、以及数据获取函数（如 `getServerSideProps`）。

**客户端环境**：包括所有标记了 `"use client"` 的组件、浏览器事件处理器、React hooks 等。

### 服务端调试步骤

1. **设置断点**：在你想要调试的服务端代码中点击行号左侧，设置红色断点。适用于 API 路由、服务端组件、数据库查询等。

2. **启动调试器**：按 F5 或在调试面板中选择 "🚀 Next.js: 服务端调试"。

3. **触发断点**：通过浏览器访问相应的页面或 API 端点。

4. **调试操作**：使用 F10（单步跳过）、F11（单步进入）、F5（继续执行）等快捷键进行调试。

### 客户端调试的最佳实践

对于客户端代码的调试，我强烈建议采用以下策略：

**方法一：使用 debugger 语句**

```javascript
'use client'

import { useState } from 'react'

export default function MyComponent() {
  const [count, setCount] = useState(0)

  const handleClick = () => {
    debugger // 浏览器会在此处暂停执行
    setCount(count + 1)
  }

  return <button onClick={handleClick}>计数: {count}</button>
}
```

**方法二：使用 console.log 进行调试**

```javascript
'use client'

import { useEffect } from 'react'

export default function MyComponent() {
  useEffect(() => {
    console.log('组件已挂载')
    console.log('当前时间:', new Date().toLocaleString())
  }, [])

  return <div>我的组件</div>
}
```

**方法三：使用浏览器开发者工具**
这是调试客户端代码最可靠的方法。在浏览器中按 F12 打开开发者工具，然后在 Sources 面板中找到你的源代码文件，直接在浏览器中设置断点。

### 数据库调试技巧

```bash
# 查看数据库连接状态
npx prisma db pull

# 重置数据库（小心使用！）
npx prisma migrate reset

# 启动数据库可视化工具
npx prisma studio
```

## 🔍 常用开发命令

### 代码质量检查

```bash
# ESLint 检查
npm run lint

# TypeScript 类型检查
npm run type-check

# 代码格式化
npm run format
```

### 测试相关

```bash
# 运行数据库连接测试
node scripts/test-supabase.js

# 测试API接口
curl http://localhost:3000/api/test-db
```

### 构建和部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 🚨 故障排除指南

### 调试器连接问题

**问题表现**：VS Code 显示"无法连接到运行时进程"或类似的连接错误。

**解决步骤**：

1. **确认应用正常运行**：在启动调试器之前，确保你的 Next.js 应用能够正常启动。在终端中运行 `npm run dev` 并等待看到启动完成的消息。

2. **检查端口占用**：

   ```bash
   # Windows
   netstat -ano | findstr :3000

   # macOS/Linux
   lsof -i :3000
   ```

3. **尝试手动启动调试模式**：

   ```bash
   node --inspect node_modules/next/dist/bin/next dev
   ```

   然后使用 "🔗 Next.js: 连接到运行中的服务器" 配置。

4. **重启 VS Code**：有时候调试器会因为之前的会话而出现问题，重启 VS Code 可以解决这个问题。

### 常见错误和解决方案

#### 1. Edge Runtime 错误：`process.on is not a function`

**错误表现：**

```
Error [TypeError]: process.on is not a function
at <unknown> (src/lib/prisma.ts:22)
```

**深入理解**：Next.js 的中间件和某些 API 路由运行在 Edge Runtime 环境中，这是一个轻量级的 JavaScript 运行时，不包含完整的 Node.js API。`process.on` 是 Node.js 特有的 API，在 Edge Runtime 中不可用。

**解决方案**：在使用 Node.js 特有的 API 前检查环境

```javascript
// 错误的写法
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

// 正确的写法
if (typeof process !== 'undefined' && process.on) {
  process.on('beforeExit', async () => {
    await prisma.$disconnect()
  })
}
```

#### 2. React 组件错误：`Event handlers cannot be passed to Client Component props`

**错误表现：**

```
Error: Event handlers cannot be passed to Client Component props.
<button onClick={function onClick} ...>
```

**深入理解**：这个错误出现是因为 Next.js 13+ 默认使用服务端组件。服务端组件在服务器上渲染，不能直接处理客户端事件。当你试图在服务端组件中使用 `onClick` 等事件处理器时，就会出现这个错误。

**解决方案**：在需要处理事件的组件文件顶部添加 `"use client"` 指令

```javascript
'use client' // 这行必须在文件的最顶部

import { Button } from '@/components/ui/button'
import { useState } from 'react'

export default function MyComponent() {
  const [count, setCount] = useState(0)

  return <Button onClick={() => setCount(count + 1)}>计数: {count}</Button>
}
```

**需要添加 "use client" 的情况**：

- 使用任何事件处理器（onClick、onChange、onSubmit 等）
- 使用 React Hooks（useState、useEffect、useContext 等）
- 访问浏览器 API（window、document、localStorage 等）
- 使用需要客户端交互的第三方库

#### 3. 客户端组件断点不生效

**问题理解**：这是一个非常常见的问题。许多开发者期望能够在 VS Code 中直接对客户端组件设置断点并进行调试，但实际上客户端代码运行在浏览器的 JavaScript 引擎中，而不是 Node.js 中。

**推荐解决方案**：

1. **使用浏览器开发者工具**：这是调试客户端代码最可靠的方法。按 F12 打开开发者工具，在 Sources 面板中找到你的源代码文件。

2. **使用 debugger 语句**：在你想要调试的位置添加 `debugger;` 语句，浏览器会自动在此处暂停执行。

3. **使用 console.log**：虽然不如断点调试强大，但 console.log 是最简单可靠的调试方法。

#### 4. 端口占用问题

**错误表现：**

```
⚠ Port 3000 is in use, using available port 3001 instead.
```

**解决方案**：

- 检查其他运行的服务：`lsof -i :3000`
- 终止占用端口的进程：`kill -9 [进程ID]`
- 或者手动指定端口：`npm run dev -- -p 3002`

#### 5. Prisma 连接错误

**错误表现**：数据库连接失败，通常伴随着连接字符串错误。

**解决方案**：

1. **检查环境变量**：确保 `.env.local` 文件中的 `DATABASE_URL` 配置正确。
2. **验证数据库连接**：`npx prisma db pull`
3. **重新生成客户端**：`npx prisma generate`
4. **检查数据库服务**：确保 Supabase 或其他数据库服务正在运行。

#### 6. TypeScript 类型错误

**解决方案**：

1. **重启 TypeScript 服务**：在 VS Code 中按 `Ctrl+Shift+P`，然后输入 "TypeScript: Restart TS Server"
2. **清理缓存**：`rm -rf .next && npm run dev`
3. **检查类型定义**：确保所有使用的库都有正确的类型定义。

## 📝 开发规范

### 代码风格

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 使用 Prettier 进行代码格式化
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

### 提交规范

```bash
# 提交格式示例
git commit -m "feat: 添加用户认证功能"
git commit -m "fix: 修复调试器连接问题"
git commit -m "docs: 更新开发环境配置指南"
```

### React 组件开发规范

**服务端组件优先原则**：默认使用服务端组件，只在确实需要客户端交互时才添加 `"use client"` 指令。这样可以获得更好的性能和 SEO。

**客户端组件使用指南**：

- 将客户端逻辑尽可能下推到叶子组件
- 避免在高层级组件中使用 `"use client"`
- 合理使用 React 的性能优化 hooks（memo、useMemo、useCallback）

**组件文件结构示例**：

```javascript
// 服务端组件（推荐的默认选择）
import { getUserData } from '@/lib/api'
import ClientButton from './ClientButton' // 导入客户端组件

export default async function UserProfile({ userId }: { userId: string }) {
  const userData = await getUserData(userId) // 服务端数据获取

  return (
    <div>
      <h1>{userData.name}</h1>
      <ClientButton userId={userId} /> {/* 客户端交互组件 */}
    </div>
  )
}
```

```javascript
// 客户端组件（只在需要交互时使用）
'use client'

import { useState } from 'react'

export default function ClientButton({ userId }: { userId: string }) {
  const [liked, setLiked] = useState(false)

  return (
    <button onClick={() => setLiked(!liked)}>
      {liked ? '❤️' : '🤍'} 点赞
    </button>
  )
}
```

## 🔧 开发环境优化

### 性能优化建议

- 使用 `next/dynamic` 进行代码分割，特别是对于大型客户端组件
- 合理使用 `memo`、`useMemo`、`useCallback` 来避免不必要的重渲染
- 避免在服务端组件中使用客户端 API
- 使用 Next.js 的图片优化功能（`next/image`）

### 开发体验优化

- 配置 Fast Refresh 以获得更好的开发体验
- 使用 TypeScript 严格模式来尽早发现潜在问题
- 配置路径别名 `@/` 指向 `src/` 目录，使导入更加简洁
- 使用 ESLint 和 Prettier 的自动修复功能

### 调试体验优化

- 在开发环境中启用详细的错误信息
- 使用 React Developer Tools 浏览器扩展
- 配置 VS Code 的自动保存功能，以便在调试时实时看到代码更改的效果

通过遵循这些指南，你应该能够建立一个高效、稳定的 Next.js 开发环境，并且能够有效地调试你的全栈应用。记住，调试是一个逐步学习的过程，不要期望一开始就能完美掌握所有技巧。随着实践的增加，你会逐渐熟悉这些工具和技术。
