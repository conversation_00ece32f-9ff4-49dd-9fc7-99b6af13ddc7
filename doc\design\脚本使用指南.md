# WorkMates 脚本使用指南

## 📋 脚本概述

本文档介绍 WorkMates 项目中可用的工具脚本，帮助开发者进行项目维护、测试和部署。

## 🛠️ 可用脚本

### 环境检查脚本

#### `scripts/check-env.js`
**功能**: 检查环境变量配置是否正确
**用法**:
```bash
node scripts/check-env.js
```
**检查项目**:
- Supabase 配置
- NextAuth.js 配置
- Google OAuth 配置
- Cloudflare R2 配置（可选）

### 数据库相关脚本

#### `scripts/test-db-connection.js`
**功能**: 测试数据库连接和基本查询
**用法**:
```bash
node scripts/test-db-connection.js
```
**测试内容**:
- 数据库连接状态
- 基本查询功能
- 表结构检查
- 用户表记录统计

#### `scripts/check-companies.js`
**功能**: 检查企业数据状态
**用法**:
```bash
node scripts/check-companies.js
```
**检查内容**:
- 企业总数统计
- 企业基本信息
- 相关数据统计（评价、薪资、面试）
- 数据完整性验证

#### `scripts/create-company-test-data.js`
**功能**: 为企业创建完整的测试数据
**用法**:
```bash
node scripts/create-company-test-data.js
```
**创建内容**:
- 企业评价数据
- 薪资数据样本
- 面试经验数据
- 多用户测试数据

### 认证系统脚本

#### `scripts/setup-google-oauth.js`
**功能**: 设置和测试 Google OAuth 配置
**用法**:
```bash
node scripts/setup-google-oauth.js
```
**功能**:
- 验证 Google OAuth 配置
- 测试认证流程
- 检查回调URL设置

#### `scripts/test-google-config.js`
**功能**: 测试 Google 配置的有效性
**用法**:
```bash
node scripts/test-google-config.js
```

#### `scripts/cleanup-invalid-sessions.js`
**功能**: 清理无效的用户会话
**用法**:
```bash
node scripts/cleanup-invalid-sessions.js
```

### 系统测试脚本

#### `scripts/test-system.js`
**功能**: 综合系统测试
**用法**:
```bash
node scripts/test-system.js
```
**测试范围**:
- 数据库连接
- API 接口
- 认证系统
- 核心功能

### 开发工具脚本

#### `scripts/dev.sh`
**功能**: 开发环境启动脚本
**用法**:
```bash
chmod +x scripts/dev.sh
./scripts/dev.sh
```
**功能**:
- 环境检查
- 依赖安装
- 数据库同步
- 开发服务器启动

## 📝 使用建议

### 项目初始化时
1. 运行 `check-env.js` 检查环境配置
2. 运行 `test-db-connection.js` 验证数据库连接
3. 运行 `setup-google-oauth.js` 配置认证系统
4. 运行 `create-company-test-data.js` 创建测试数据

### 日常开发时
1. 使用 `dev.sh` 启动开发环境
2. 使用 `test-system.js` 进行系统测试
3. 使用 `check-companies.js` 验证数据状态

### 问题排查时
1. 运行 `check-env.js` 检查配置问题
2. 运行 `test-db-connection.js` 检查数据库问题
3. 运行 `test-google-config.js` 检查认证问题
4. 运行 `cleanup-invalid-sessions.js` 清理会话问题

## ⚠️ 注意事项

### 环境要求
- Node.js 18.0+
- 有效的 `.env.local` 配置文件
- Supabase 数据库访问权限

### 安全提醒
- 不要在生产环境运行测试脚本
- 确保环境变量安全
- 定期清理测试数据

### 错误处理
- 所有脚本都包含错误处理
- 查看控制台输出获取详细信息
- 检查网络连接和权限设置

## 🔧 脚本维护

### 添加新脚本
1. 在 `scripts/` 目录创建新文件
2. 添加适当的错误处理
3. 更新本文档
4. 添加使用示例

### 脚本规范
- 使用 CommonJS 模块格式
- 包含详细的控制台输出
- 实现适当的错误处理
- 添加必要的注释

## 📚 相关文档

- [环境配置指南](./开发环境配置指南.md)
- [数据库设计文档](./WorkMates数据库完整设计文档.md)
- [OAuth认证指南](./OAuth认证系统实现指南.md)
- [Supabase配置指南](./Supabase接入配置指南.md)

---

**💡 提示**: 如果遇到脚本执行问题，请先检查环境配置和网络连接，然后查看具体的错误信息进行排查。
