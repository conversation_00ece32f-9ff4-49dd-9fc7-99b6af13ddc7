import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 面试轮次验证模式
const interviewRoundSchema = z.object({
  round: z.number().min(1),
  type: z.string().min(1),
  duration: z.number().optional(),
  questions: z.string().optional(),
  feedback: z.string().optional(),
})

// 创建面试经验的验证模式
const createInterviewSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(200, '标题最多200个字符'),
  company: z.string().min(1, '公司名称不能为空'),
  position: z.string().min(1, '职位不能为空'),
  location: z.string().optional(),
  department: z.string().optional(),
  level: z.string().optional(),
  result: z.enum(['PASSED', 'FAILED', 'PENDING', 'CANCELLED']),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD', 'VERY_HARD']),
  rating: z.string().optional(),
  recommendation: z.string().optional(),
  processTime: z.string().optional(),
  rounds: z.array(interviewRoundSchema).default([]),
  summary: z.string().optional(),
  advice: z.string().optional(),
  preparation: z.string().optional(),
  tags: z.array(z.string()).default([]),
  isAnonymous: z.boolean().default(true),
})

/**
 * 提交面试经验
 * POST /api/interviews
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再提交面试经验',
          },
        },
        { status: 401 }
      )
    }

    const body = await request.json()

    // 验证输入数据
    const validatedData = createInterviewSchema.parse(body)

    // 查找或创建公司
    let company = await prisma.company.findFirst({
      where: {
        name: {
          equals: validatedData.company,
          mode: 'insensitive',
        },
      },
    })

    if (!company) {
      // 如果公司不存在，创建新公司
      company = await prisma.company.create({
        data: {
          name: validatedData.company,
          isActive: true,
        },
      })
    }

    // 创建面试经验记录
    const interview = await prisma.interview.create({
      data: {
        position: validatedData.position,
        department: validatedData.department,
        interviewType: 'ONSITE', // 默认值
        interviewRound: validatedData.rounds.length || 1,
        difficulty: validatedData.difficulty,
        result: validatedData.result,
        rating: validatedData.rating ? parseInt(validatedData.rating) : null,
        questions: validatedData.rounds.map(r => r.questions).filter(Boolean).join('\n\n'),
        experience: validatedData.summary || '',
        tips: validatedData.advice || '',
        notes: validatedData.preparation || '',
        isAnonymous: validatedData.isAnonymous,
        authorId: session.user.id,
        companyId: company.id,
      },
      select: {
        id: true,
        position: true,
        difficulty: true,
        result: true,
        isAnonymous: true,
        createdAt: true,
        company: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return NextResponse.json(
      {
        success: true,
        message: '面试经验提交成功',
        data: interview,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('提交面试经验失败:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入数据格式',
            details: error.errors,
          },
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        message: '提交失败',
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
