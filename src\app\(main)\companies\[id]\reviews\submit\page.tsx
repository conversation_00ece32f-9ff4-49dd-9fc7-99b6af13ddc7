'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Textarea } from '@/components/ui/textarea'
import {
    ArrowLeft,
    Info,
    MessageSquare,
    Shield,
    Star,
    User
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

interface CompanyReviewSubmitPageProps {
  params: {
    id: string
  }
}

/**
 * 企业评价提交页面
 * 允许用户提交企业工作体验评价
 */
export default function CompanyReviewSubmitPage({ params }: CompanyReviewSubmitPageProps) {
  const router = useRouter()
  const companyId = params.id
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const [formData, setFormData] = useState({
    // 基本信息
    title: '',
    position: '',
    department: '',
    workPeriod: '',
    employmentStatus: '',
    
    // 评分
    overallRating: [4],
    salaryRating: [4],
    environmentRating: [4],
    managementRating: [4],
    growthRating: [4],
    balanceRating: [4],
    
    // 评价内容
    pros: '',
    cons: '',
    advice: '',
    workCulture: '',
    
    // 详细信息
    workHours: '',
    benefits: [] as string[],
    tags: [] as string[],
    
    // 其他信息
    wouldRecommend: '',
    isAnonymous: true,
    agreeTerms: false,
  })

  const handleInputChange = (field: string, value: string | boolean | string[] | number[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleBenefitToggle = (benefit: string) => {
    setFormData(prev => ({
      ...prev,
      benefits: prev.benefits.includes(benefit)
        ? prev.benefits.filter(b => b !== benefit)
        : [...prev.benefits, benefit]
    }))
  }

  const handleTagToggle = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.agreeTerms) {
      alert('请同意评价条款')
      return
    }

    setIsSubmitting(true)
    try {
      // TODO: 实际提交到API
      await new Promise(resolve => setTimeout(resolve, 2000))
      alert('评价提交成功！感谢您的分享')
      router.push(`/companies/${companyId}/reviews`)
    } catch (error) {
      console.error('提交评价失败:', error)
      alert('提交失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  // 模拟获取企业信息
  const company = {
    id: companyId,
    name: '阿里巴巴',
    industry: '互联网',
    location: '杭州'
  }

  const benefitOptions = [
    '五险一金',
    '补充医疗保险',
    '年终奖',
    '季度奖金',
    '项目奖金',
    '股票期权',
    '免费餐食',
    '交通补助',
    '住房补助',
    '培训机会',
    '弹性工作',
    '带薪年假',
    '节日福利',
    '团建活动',
    '健身房',
    '午休室',
    '免费班车',
    '员工折扣'
  ]

  const tagOptions = [
    '技术氛围好',
    '薪资不错',
    '福利完善',
    '团队和谐',
    '管理规范',
    '发展空间大',
    '学习机会多',
    '工作轻松',
    '压力大',
    '加班多',
    '竞争激烈',
    '晋升难',
    '制度完善',
    '文化开放',
    '办公环境好',
    '领导nice'
  ]

  const RatingSlider = ({ 
    label, 
    value, 
    onChange 
  }: { 
    label: string
    value: number[]
    onChange: (value: number[]) => void 
  }) => (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <Label className="text-sm font-medium">{label}</Label>
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="font-bold">{value[0]}</span>
        </div>
      </div>
      <Slider
        value={value}
        onValueChange={onChange}
        max={5}
        min={1}
        step={0.5}
        className="w-full"
      />
      <div className="flex justify-between text-xs text-gray-500">
        <span>1分</span>
        <span>5分</span>
      </div>
    </div>
  )

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 页面标题 */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <h1 className="text-3xl font-bold mb-2">评价企业</h1>
        <p className="text-gray-600 mb-4">
          分享您在 <span className="font-semibold">{company.name}</span> 的工作体验
        </p>
        <div className="text-sm text-gray-500">
          {company.industry} • {company.location}
        </div>
      </div>

      {/* 匿名提示 */}
      <Alert className="mb-8">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          我们支持匿名评价。您的个人信息将被保护，只有工作体验会被公开展示。
        </AlertDescription>
      </Alert>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              基本信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">评价标题 *</Label>
              <Input
                id="title"
                placeholder="简要描述您的工作体验"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="position">职位名称 *</Label>
                <Input
                  id="position"
                  placeholder="如：前端工程师"
                  value={formData.position}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">部门</Label>
                <Input
                  id="department"
                  placeholder="如：技术部"
                  value={formData.department}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="workPeriod">工作时间 *</Label>
                <Select value={formData.workPeriod} onValueChange={(value) => handleInputChange('workPeriod', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择工作时长" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0-6m">0-6个月</SelectItem>
                    <SelectItem value="6m-1y">6个月-1年</SelectItem>
                    <SelectItem value="1-2y">1-2年</SelectItem>
                    <SelectItem value="2-3y">2-3年</SelectItem>
                    <SelectItem value="3-5y">3-5年</SelectItem>
                    <SelectItem value="5y+">5年以上</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="employmentStatus">在职状态 *</Label>
                <Select value={formData.employmentStatus} onValueChange={(value) => handleInputChange('employmentStatus', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择在职状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="current">在职</SelectItem>
                    <SelectItem value="former">前员工</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 评分 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              评分
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <RatingSlider
              label="总体评价"
              value={formData.overallRating}
              onChange={(value) => handleInputChange('overallRating', value)}
            />
            
            <div className="grid gap-6 md:grid-cols-2">
              <RatingSlider
                label="薪资水平"
                value={formData.salaryRating}
                onChange={(value) => handleInputChange('salaryRating', value)}
              />
              <RatingSlider
                label="工作环境"
                value={formData.environmentRating}
                onChange={(value) => handleInputChange('environmentRating', value)}
              />
              <RatingSlider
                label="管理制度"
                value={formData.managementRating}
                onChange={(value) => handleInputChange('managementRating', value)}
              />
              <RatingSlider
                label="发展前景"
                value={formData.growthRating}
                onChange={(value) => handleInputChange('growthRating', value)}
              />
            </div>

            <RatingSlider
              label="工作生活平衡"
              value={formData.balanceRating}
              onChange={(value) => handleInputChange('balanceRating', value)}
            />
          </CardContent>
        </Card>

        {/* 详细评价 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              详细评价
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pros">优点 *</Label>
              <Textarea
                id="pros"
                placeholder="请分享这家公司的优点，如工作环境、团队氛围、薪资福利等..."
                value={formData.pros}
                onChange={(e) => handleInputChange('pros', e.target.value)}
                rows={4}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cons">缺点</Label>
              <Textarea
                id="cons"
                placeholder="请客观分享这家公司存在的不足之处..."
                value={formData.cons}
                onChange={(e) => handleInputChange('cons', e.target.value)}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="workCulture">企业文化</Label>
              <Textarea
                id="workCulture"
                placeholder="描述公司的企业文化和工作氛围..."
                value={formData.workCulture}
                onChange={(e) => handleInputChange('workCulture', e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="advice">给求职者的建议</Label>
              <Textarea
                id="advice"
                placeholder="给想加入这家公司的求职者一些建议..."
                value={formData.advice}
                onChange={(e) => handleInputChange('advice', e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* 补充信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              补充信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="workHours">平均工作时长</Label>
              <Select value={formData.workHours} onValueChange={(value) => handleInputChange('workHours', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择工作时长" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="8h">8小时/天</SelectItem>
                  <SelectItem value="9h">9小时/天</SelectItem>
                  <SelectItem value="10h">10小时/天</SelectItem>
                  <SelectItem value="11h">11小时/天</SelectItem>
                  <SelectItem value="12h+">12小时以上/天</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>福利待遇 (可多选)</Label>
              <div className="grid gap-2 md:grid-cols-3">
                {benefitOptions.map(benefit => (
                  <div key={benefit} className="flex items-center space-x-2">
                    <Checkbox
                      id={benefit}
                      checked={formData.benefits.includes(benefit)}
                      onCheckedChange={() => handleBenefitToggle(benefit)}
                    />
                    <Label htmlFor={benefit} className="text-sm">
                      {benefit}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-3">
              <Label>标签 (可多选)</Label>
              <div className="flex flex-wrap gap-2">
                {tagOptions.map(tag => (
                  <Badge
                    key={tag}
                    variant={formData.tags.includes(tag) ? 'default' : 'outline'}
                    className="cursor-pointer"
                    onClick={() => handleTagToggle(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="wouldRecommend">是否推荐朋友来这家公司工作？</Label>
              <Select value={formData.wouldRecommend} onValueChange={(value) => handleInputChange('wouldRecommend', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择推荐程度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="highly">强烈推荐</SelectItem>
                  <SelectItem value="yes">推荐</SelectItem>
                  <SelectItem value="neutral">中性</SelectItem>
                  <SelectItem value="no">不推荐</SelectItem>
                  <SelectItem value="strongly-no">强烈不推荐</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isAnonymous"
                  checked={formData.isAnonymous}
                  onCheckedChange={(checked) => handleInputChange('isAnonymous', checked)}
                />
                <Label htmlFor="isAnonymous">
                  匿名评价（推荐）
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="agreeTerms"
                  checked={formData.agreeTerms}
                  onCheckedChange={(checked) => handleInputChange('agreeTerms', checked)}
                />
                <Label htmlFor="agreeTerms" className="text-sm">
                  我同意评价条款，承诺提供真实客观的评价内容
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 提交按钮 */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            取消
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !formData.agreeTerms || !formData.title || !formData.position || !formData.pros}
          >
            {isSubmitting ? '提交中...' : '提交评价'}
          </Button>
        </div>
      </form>
    </div>
  )
} 