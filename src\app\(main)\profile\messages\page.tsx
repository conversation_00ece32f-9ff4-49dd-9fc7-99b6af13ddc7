'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
    Bell,
    Check,
    CheckCheck,
    Filter,
    Heart,
    MessageSquare,
    Search,
    Trash2
} from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'

/**
 * 用户消息中心页面
 * 包含私信、系统通知、互动消息等
 */
export default function MessagesPage() {
  const [activeTab, setActiveTab] = useState('all')
  const [selectedMessages, setSelectedMessages] = useState<string[]>([])

  const handleSelectMessage = (messageId: string) => {
    setSelectedMessages(prev => 
      prev.includes(messageId)
        ? prev.filter(id => id !== messageId)
        : [...prev, messageId]
    )
  }

  const handleSelectAll = () => {
    if (selectedMessages.length === messages.length) {
      setSelectedMessages([])
    } else {
      setSelectedMessages(messages.map(m => m.id))
    }
  }

  const handleMarkAsRead = () => {
    // TODO: 实际标记已读逻辑
    console.log('标记已读:', selectedMessages)
    setSelectedMessages([])
  }

  const handleDelete = () => {
    // TODO: 实际删除逻辑
    console.log('删除消息:', selectedMessages)
    setSelectedMessages([])
  }

  // 模拟消息数据
  const messages = [
    {
      id: '1',
      type: 'system',
      title: '欢迎加入 WorkMates',
      content: '感谢您注册 WorkMates！开始探索职场信息，分享您的工作经验吧。',
      isRead: false,
      createdAt: '2024-01-20 10:30',
      sender: {
        name: '系统消息',
        avatar: '/system-avatar.png',
        type: 'system'
      }
    },
    {
      id: '2',
      type: 'interaction',
      title: '有人点赞了您的面经',
      content: '用户 "职场新人" 点赞了您分享的《阿里巴巴前端工程师面试经验》',
      isRead: false,
      createdAt: '2024-01-19 15:45',
      sender: {
        name: '职场新人',
        avatar: '/avatar1.jpg',
        type: 'user'
      },
      relatedContent: {
        type: 'interview',
        title: '阿里巴巴前端工程师面试经验',
        url: '/companies/interviews/1'
      }
    },
    {
      id: '3',
      type: 'private',
      title: '关于面试问题的咨询',
      content: '您好，看到您分享的面经很有帮助，想请教一下关于算法题的准备...',
      isRead: true,
      createdAt: '2024-01-19 09:20',
      sender: {
        name: '求职小白',
        avatar: '/avatar2.jpg',
        type: 'user'
      }
    },
    {
      id: '4',
      type: 'interaction',
      title: '有人评论了您的薪资分享',
      content: '用户 "HR姐姐" 评论了您的薪资信息：这个薪资水平确实符合市场行情',
      isRead: true,
      createdAt: '2024-01-18 14:15',
      sender: {
        name: 'HR姐姐',
        avatar: '/avatar3.jpg',
        type: 'user'
      },
      relatedContent: {
        type: 'salary',
        title: '阿里巴巴前端工程师薪资',
        url: '/companies/salaries'
      }
    },
    {
      id: '5',
      type: 'system',
      title: '您的企业评价已发布',
      content: '您对阿里巴巴的工作体验评价已成功发布，感谢您的分享！',
      isRead: true,
      createdAt: '2024-01-17 16:30',
      sender: {
        name: '系统消息',
        avatar: '/system-avatar.png',
        type: 'system'
      }
    }
  ]

  const getMessagesByType = (type: string) => {
    if (type === 'all') return messages
    return messages.filter(m => m.type === type)
  }

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'system':
        return <Bell className="h-4 w-4 text-blue-600" />
      case 'private':
        return <MessageSquare className="h-4 w-4 text-green-600" />
      case 'interaction':
        return <Heart className="h-4 w-4 text-red-600" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getMessageBadge = (type: string) => {
    switch (type) {
      case 'system':
        return '系统'
      case 'private':
        return '私信'
      case 'interaction':
        return '互动'
      default:
        return '消息'
    }
  }

  const unreadCount = messages.filter(m => !m.isRead).length

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">消息中心</h1>
            <p className="text-gray-600">
              {unreadCount > 0 ? `您有 ${unreadCount} 条未读消息` : '所有消息已读'}
            </p>
          </div>
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            筛选
          </Button>
        </div>
      </div>

      {/* 搜索和操作栏 */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input placeholder="搜索消息..." className="pl-10" />
              </div>
              
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="消息类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="unread">未读</SelectItem>
                  <SelectItem value="system">系统</SelectItem>
                  <SelectItem value="private">私信</SelectItem>
                  <SelectItem value="interaction">互动</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {selectedMessages.length > 0 && (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={handleMarkAsRead}>
                  <Check className="mr-2 h-4 w-4" />
                  标记已读
                </Button>
                <Button variant="outline" size="sm" onClick={handleDelete}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  删除
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 消息列表 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full max-w-md grid-cols-4">
          <TabsTrigger value="all" className="relative">
            全部
            {unreadCount > 0 && (
              <Badge variant="destructive" className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="system">系统</TabsTrigger>
          <TabsTrigger value="private">私信</TabsTrigger>
          <TabsTrigger value="interaction">互动</TabsTrigger>
        </TabsList>

        {/* 全部消息 */}
        <TabsContent value="all" className="space-y-4">
          <MessageList 
            messages={getMessagesByType('all')} 
            selectedMessages={selectedMessages}
            onSelectMessage={handleSelectMessage}
            onSelectAll={handleSelectAll}
            getMessageIcon={getMessageIcon}
            getMessageBadge={getMessageBadge}
          />
        </TabsContent>

        {/* 系统消息 */}
        <TabsContent value="system" className="space-y-4">
          <MessageList 
            messages={getMessagesByType('system')} 
            selectedMessages={selectedMessages}
            onSelectMessage={handleSelectMessage}
            onSelectAll={handleSelectAll}
            getMessageIcon={getMessageIcon}
            getMessageBadge={getMessageBadge}
          />
        </TabsContent>

        {/* 私信 */}
        <TabsContent value="private" className="space-y-4">
          <MessageList 
            messages={getMessagesByType('private')} 
            selectedMessages={selectedMessages}
            onSelectMessage={handleSelectMessage}
            onSelectAll={handleSelectAll}
            getMessageIcon={getMessageIcon}
            getMessageBadge={getMessageBadge}
          />
        </TabsContent>

        {/* 互动消息 */}
        <TabsContent value="interaction" className="space-y-4">
          <MessageList 
            messages={getMessagesByType('interaction')} 
            selectedMessages={selectedMessages}
            onSelectMessage={handleSelectMessage}
            onSelectAll={handleSelectAll}
            getMessageIcon={getMessageIcon}
            getMessageBadge={getMessageBadge}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 消息列表组件
 */
function MessageList({ 
  messages, 
  selectedMessages, 
  onSelectMessage, 
  onSelectAll,
  getMessageIcon,
  getMessageBadge
}: {
  messages: any[]
  selectedMessages: string[]
  onSelectMessage: (id: string) => void
  onSelectAll: () => void
  getMessageIcon: (type: string) => React.ReactNode
  getMessageBadge: (type: string) => string
}) {
  if (messages.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无消息</h3>
          <p className="text-gray-600">当有新消息时，会显示在这里</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* 全选按钮 */}
      {messages.length > 0 && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <input
            type="checkbox"
            checked={selectedMessages.length === messages.length}
            onChange={onSelectAll}
            className="rounded"
          />
          <span>全选 ({messages.length} 条消息)</span>
        </div>
      )}

      {/* 消息列表 */}
      {messages.map(message => (
        <Card 
          key={message.id} 
          className={`cursor-pointer transition-colors ${
            !message.isRead ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
          } ${
            selectedMessages.includes(message.id) ? 'ring-2 ring-blue-500' : ''
          }`}
          onClick={() => onSelectMessage(message.id)}
        >
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              {/* 选择框 */}
              <input
                type="checkbox"
                checked={selectedMessages.includes(message.id)}
                onChange={() => onSelectMessage(message.id)}
                className="mt-1 rounded"
                onClick={(e) => e.stopPropagation()}
              />

              {/* 头像 */}
              <Avatar className="h-10 w-10">
                <AvatarImage src={message.sender.avatar} />
                <AvatarFallback>{message.sender.name[0]}</AvatarFallback>
              </Avatar>

              {/* 消息内容 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-2">
                    {getMessageIcon(message.type)}
                    <span className="font-medium text-gray-900">
                      {message.title}
                    </span>
                    <Badge variant="secondary" className="text-xs">
                      {getMessageBadge(message.type)}
                    </Badge>
                    {!message.isRead && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500">
                      {message.createdAt}
                    </span>
                    {message.isRead ? (
                      <CheckCheck className="h-4 w-4 text-blue-600" />
                    ) : (
                      <Check className="h-4 w-4 text-gray-400" />
                    )}
                  </div>
                </div>

                <p className="text-gray-700 text-sm mb-2 line-clamp-2">
                  {message.content}
                </p>

                {/* 相关内容 */}
                {message.relatedContent && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-xs">
                    <span className="text-gray-600">相关内容：</span>
                    <Link 
                      href={message.relatedContent.url}
                      className="text-blue-600 hover:underline ml-1"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {message.relatedContent.title}
                    </Link>
                  </div>
                )}

                {/* 发送者信息 */}
                <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                  <span>来自: {message.sender.name}</span>
                  {message.type === 'private' && (
                    <Button size="sm" variant="outline" className="h-6 text-xs">
                      回复
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 