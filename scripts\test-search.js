/**
 * 搜索功能测试脚本
 * 用于验证搜索API的各种功能
 */

const BASE_URL = 'http://localhost:3000'

// 测试用例
const testCases = [
  {
    name: '基础搜索测试',
    url: '/api/search?q=腾讯',
    expected: {
      success: true,
      hasData: true,
      hasCompanies: true,
      hasPosts: true,
      hasUsers: true
    }
  },
  {
    name: '企业搜索测试',
    url: '/api/search/companies?q=科技',
    expected: {
      success: true,
      hasData: true
    }
  },
  {
    name: '帖子搜索测试',
    url: '/api/search/posts?q=面试',
    expected: {
      success: true,
      hasData: true
    }
  },
  {
    name: '用户搜索测试',
    url: '/api/search/users?q=张',
    expected: {
      success: true,
      hasData: true
    }
  },
  {
    name: '空搜索测试',
    url: '/api/search?q=',
    expected: {
      success: false
    }
  },
  {
    name: '无结果搜索测试',
    url: '/api/search?q=xyz123abc',
    expected: {
      success: true,
      hasData: true,
      isEmpty: true
    }
  }
]

// 执行测试
async function runTests() {
  console.log('🚀 开始搜索功能测试...\n')
  
  let passedTests = 0
  let totalTests = testCases.length
  
  for (const testCase of testCases) {
    try {
      console.log(`📋 测试: ${testCase.name}`)
      console.log(`🔗 URL: ${testCase.url}`)
      
      const response = await fetch(`${BASE_URL}${testCase.url}`)
      const data = await response.json()
      
      console.log(`📊 状态码: ${response.status}`)
      console.log(`📄 响应数据:`, JSON.stringify(data, null, 2).substring(0, 200) + '...')
      
      // 验证测试结果
      let testPassed = true
      const errors = []
      
      if (testCase.expected.success !== undefined) {
        if (data.success !== testCase.expected.success) {
          testPassed = false
          errors.push(`期望 success: ${testCase.expected.success}, 实际: ${data.success}`)
        }
      }
      
      if (testCase.expected.hasData && !data.data) {
        testPassed = false
        errors.push('期望有数据，但 data 字段为空')
      }
      
      if (testCase.expected.hasCompanies && (!data.data.companies || data.data.companies.length === 0)) {
        console.log('⚠️  警告: 没有企业搜索结果（可能是正常的）')
      }
      
      if (testCase.expected.hasPosts && (!data.data.posts || data.data.posts.length === 0)) {
        console.log('⚠️  警告: 没有帖子搜索结果（可能是正常的）')
      }
      
      if (testCase.expected.hasUsers && (!data.data.users || data.data.users.length === 0)) {
        console.log('⚠️  警告: 没有用户搜索结果（可能是正常的）')
      }
      
      if (testCase.expected.isEmpty) {
        const totalResults = (data.data?.companies?.length || 0) + 
                           (data.data?.posts?.length || 0) + 
                           (data.data?.users?.length || 0)
        if (totalResults > 0) {
          console.log('⚠️  警告: 期望无结果，但找到了结果')
        }
      }
      
      if (testPassed) {
        console.log('✅ 测试通过\n')
        passedTests++
      } else {
        console.log('❌ 测试失败:')
        errors.forEach(error => console.log(`   - ${error}`))
        console.log('')
      }
      
    } catch (error) {
      console.log(`❌ 测试失败: ${error.message}\n`)
    }
  }
  
  console.log(`📈 测试结果: ${passedTests}/${totalTests} 通过`)
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！搜索功能正常工作。')
  } else {
    console.log('⚠️  部分测试失败，请检查上述错误。')
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = { runTests, testCases }
