-- WorkMates 数据库种子数据
-- 版本: 1.1
-- 最后更新: 2025-07-14
-- 此文件包含用于为 WorkMates 数据库填充初始数据的种子数据，主要用于测试和开发目的。

BEGIN;

-- ================================
-- 清理现有数据 (用于开发环境)
-- ================================

-- 在开发环境中，清除现有数据以确保一个干净的状态通常很有用。
-- 取消以下行的注释，以便在填充数据前清空所有表。
-- 警告: 这将删除指定表中的所有数据。
/*
TRUNCATE TABLE 
    "users", "companies", "posts", "comments", "likes", "bookmarks", 
    "salaries", "interviews", "ratings", "reports", "work_experiences", 
    "experience_files", "verification_records", "user_credibility", 
    "file_uploads", "notifications", "notification_settings" 
RESTART IDENTITY CASCADE;
*/

-- ================================
-- 填充用户数据
-- ================================

-- 插入一个默认的管理员用户和几个示例用户。
-- 密码应使用像 bcrypt 这样的安全算法进行哈希处理。
INSERT INTO "users" ("email", "name", "username", "level", "isVerified") VALUES
('<EMAIL>', '管理员', 'admin', 'ADMIN', true),
('<EMAIL>', '张三', 'zhang.san', 'ACTIVE', true),
('<EMAIL>', '李四', 'li.si', 'NEWBIE', false);


-- ================================
-- 填充公司数据
-- ================================

-- 插入几个示例公司。
INSERT INTO "companies" ("name", "nameEn", "industry", "size", "isVerified") VALUES
('科技创新有限公司', 'Tech Innovators Inc.', '技术', 'LARGE', true),
('创意解决方案有限责任公司', 'Creative Solutions LLC', '市场营销', 'MEDIUM', true),
('绿色星球商品', 'Green Planet Goods', '零售', 'SMALL', false);


COMMIT;
