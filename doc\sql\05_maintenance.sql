-- WorkMates 数据库维护脚本
-- 版本: 1.1
-- 最后更新: 2025-07-14
-- 此文件包含用于数据库维护任务的脚本，例如清理(vacuum)和重建索引(reindex)。

-- ================================
-- 维护函数
-- ================================

-- 此函数对数据库执行完整的清理和分析。
-- VACUUM FULL 可以回收更多空间，但会锁定表。
-- ANALYZE 更新查询规划器使用的统计信息。
-- VERBOSE 提供详细的输出。
-- 注意: 运行 VACUUM FULL 可能会很慢，并且需要排他锁。
CREATE OR REPLACE FUNCTION vacuum_analyze_full()
RETURNS VOID AS $$
BEGIN
    VACUUM (FULL, ANALYZE, VERBOSE);
END;
$$ LANGUAGE plpgsql;

-- 此函数并发地重建数据库索引。
-- REINDEX DATABASE CONCURRENTLY 重建所有索引，而不会锁定写操作。
-- 这对于不能停机的生产环境非常有用。
-- 注意: 此命令不能在事务块内执行。
CREATE OR REPLACE FUNCTION reindex_database_concurrently()
RETURNS VOID AS $$
BEGIN
    REINDEX DATABASE CONCURRENTLY workmates;
END;
$$ LANGUAGE plpgsql;
