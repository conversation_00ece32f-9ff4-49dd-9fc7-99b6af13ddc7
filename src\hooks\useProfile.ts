'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState } from 'react'

interface UserProfile {
  id: string
  name: string | null
  email: string | null
  username: string | null
  phone: string | null
  bio: string | null
  avatar: string | null
  avatarKey: string | null
  company: string | null
  position: string | null
  industry: string | null
  education: string | null
  experience: number | null
  skills: string[]
  level: string
  points: number
  reputation: number
  isVerified: boolean

  // 新增字段
  storageUsed: number
  storageLimit: number
  profileCompleteness: number
  lastProfileUpdate: string | null
  followersCount: number
  followingCount: number
  twoFactorEnabled: boolean

  createdAt: string
  updatedAt: string
  stats: {
    postsCount: number
    commentsCount: number
    ratingsCount: number
    salariesCount: number
    interviewsCount: number
    workExperiencesCount: number
    totalContributions: number
  }
}

interface UseProfileReturn {
  profile: UserProfile | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useProfile(): UseProfileReturn {
  const { data: session } = useSession()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProfile = async () => {
    if (!session?.user?.id) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/users/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        setProfile(data.data)
      } else {
        throw new Error(data.message || '获取用户信息失败')
      }
    } catch (err) {
      console.error('获取用户信息失败:', err)
      setError(err instanceof Error ? err.message : '获取用户信息失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProfile()
  }, [session?.user?.id])

  return {
    profile,
    loading,
    error,
    refetch: fetchProfile,
  }
}

// 更新用户资料的hook
export function useUpdateProfile() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateProfile = async (data: Partial<UserProfile>) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message || '更新用户信息失败')
      }

      return result.data
    } catch (err) {
      console.error('更新用户信息失败:', err)
      const errorMessage = err instanceof Error ? err.message : '更新用户信息失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return {
    updateProfile,
    loading,
    error,
  }
}
