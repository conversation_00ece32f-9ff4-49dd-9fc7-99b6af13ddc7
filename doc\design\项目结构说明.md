# WorkMates 项目结构说明

## 目录结构概览

```
WorkMates/
├── src/                              # 源代码目录
│   ├── app/                          # Next.js 15 App Router
│   │   ├── (main)/                   # 主要功能页面组
│   │   │   ├── companies/            # 企业信息模块 ✅
│   │   │   │   ├── [id]/             # 企业详情页 ✅
│   │   │   │   │   ├── page.tsx      # 企业详情主页 ✅
│   │   │   │   │   └── reviews/      # 企业评价页面
│   │   │   │   │       ├── page.tsx  # 评价列表 ✅
│   │   │   │   │       └── submit/   # 提交评价
│   │   │   │   ├── interviews/       # 面试经验页面
│   │   │   │   │   ├── [id]/         # 面试详情页 ✅
│   │   │   │   │   └── page.tsx      # 面试列表页 ✅
│   │   │   │   ├── salaries/         # 薪资信息页面
│   │   │   │   │   └── page.tsx      # 薪资列表页 ✅
│   │   │   │   └── page.tsx          # 企业列表页 ✅
│   │   │   ├── forum/                # 论坛社区模块 ✅
│   │   │   │   ├── [id]/             # 帖子详情页
│   │   │   │   ├── create/           # 发帖页面 ✅
│   │   │   │   └── page.tsx          # 论坛首页 ✅
│   │   │   ├── profile/              # 用户中心模块
│   │   │   │   ├── favorites/        # 用户收藏
│   │   │   │   ├── messages/         # 用户消息
│   │   │   │   ├── settings/         # 用户设置
│   │   │   │   ├── work-experience/  # 工作经验管理
│   │   │   │   └── page.tsx          # 个人主页
│   │   │   └── layout.tsx            # 主要页面布局
│   │   ├── auth/                     # 认证相关页面
│   │   │   ├── login/                # 登录页面
│   │   │   └── register/             # 注册页面
│   │   ├── search/                   # 全局搜索页面
│   │   ├── about/                    # 关于我们页面
│   │   ├── help/                     # 帮助中心页面
│   │   ├── privacy/                  # 隐私政策页面
│   │   ├── terms/                    # 服务条款页面
│   │   ├── api/                      # API 路由
│   │   │   ├── auth/                 # 认证相关API
│   │   │   │   └── [...nextauth]/    # NextAuth.js 处理 ✅
│   │   │   ├── companies/            # 企业相关API ✅
│   │   │   │   ├── [id]/             # 企业详情API ✅
│   │   │   │   └── route.ts          # 企业列表API ✅
│   │   │   ├── posts/                # 帖子相关API ✅
│   │   │   │   └── route.ts          # 帖子CRUD ✅
│   │   │   ├── search/               # 搜索相关API
│   │   │   │   └── companies/        # 企业搜索 ✅
│   │   │   ├── users/                # 用户相关API
│   │   │   │   └── profile/          # 用户资料 ✅
│   │   │   ├── debug/                # 调试API
│   │   │   │   └── session/          # 会话调试 ✅
│   │   │   └── upload/               # 文件上传API (开发中)
│   │   ├── globals.css               # 全局样式
│   │   ├── layout.tsx                # 根布局
│   │   ├── page.tsx                  # 首页
│   │   └── not-found.tsx             # 404页面
│   ├── components/                   # 可复用组件
│   │   ├── ui/                       # 基础UI组件 (shadcn/ui)
│   │   │   ├── button.tsx            # 按钮组件
│   │   │   ├── card.tsx              # 卡片组件
│   │   │   ├── input.tsx             # 输入框组件
│   │   │   ├── dialog.tsx            # 对话框组件
│   │   │   └── ...                   # 其他UI组件
│   │   ├── layout/                   # 布局组件
│   │   │   ├── header.tsx            # 页头组件
│   │   │   ├── footer.tsx            # 页脚组件
│   │   │   └── navigation.tsx        # 导航组件
│   │   ├── features/                 # 功能特定组件
│   │   │   ├── companies/            # 企业相关组件
│   │   │   ├── forum/                # 论坛相关组件
│   │   │   └── users/                # 用户相关组件
│   │   └── providers.tsx             # Context Providers
│   ├── lib/                          # 工具库和配置
│   │   ├── auth.ts                   # NextAuth.js 配置
│   │   ├── prisma.ts                 # Prisma 客户端
│   │   ├── supabase/                 # Supabase 客户端
│   │   │   ├── client.ts             # 浏览器端客户端
│   │   │   └── server.ts             # 服务端客户端
│   │   └── utils.ts                  # 工具函数
│   ├── types/                        # TypeScript 类型定义
│   │   ├── database.ts               # 数据库类型
│   │   └── work-experience.ts        # 工作经验类型
│   └── middleware.ts                 # Next.js 中间件
├── prisma/                           # Prisma 配置
│   ├── schema.prisma                 # 数据库模型
│   └── seed.ts                       # 种子数据
├── doc/                              # 项目文档
│   ├── design/                       # 设计文档
│   └── sql/                          # SQL 脚本
├── scripts/                          # 工具脚本
│   ├── test-db-connection.js         # 数据库连接测试 ✅
│   ├── check-env.js                  # 环境变量检查 ✅
│   ├── check-companies.js            # 企业数据检查 ✅
│   ├── create-company-test-data.js   # 创建测试数据 ✅
│   ├── setup-google-oauth.js         # Google OAuth 设置 ✅
│   ├── test-google-config.js         # Google 配置测试 ✅
│   ├── cleanup-invalid-sessions.js   # 清理无效会话 ✅
│   ├── test-system.js                # 系统测试 ✅
│   └── dev.sh                        # 开发启动脚本 ✅
├── public/                           # 静态资源
├── .env.example                      # 环境变量示例
├── package.json                      # 项目依赖
├── next.config.js                    # Next.js 配置
├── tailwind.config.ts                # Tailwind CSS 配置
├── tsconfig.json                     # TypeScript 配置
└── README.md                         # 项目说明
```

## 核心技术栈

### 前端框架
- **Next.js 15**: 基于React的全栈框架，使用App Router
- **React 18**: 前端UI库
- **TypeScript**: 静态类型检查

### 样式和UI
- **Tailwind CSS**: 原子化CSS框架
- **shadcn/ui**: 基于Radix UI的组件库
- **Lucide React**: 图标库

### 数据库和ORM
- **Supabase PostgreSQL**: 主数据库
- **Prisma**: ORM和数据库工具

### 认证
- **NextAuth.js v5**: 认证解决方案
- **bcryptjs**: 密码加密

### 开发工具
- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **tsx**: TypeScript执行器

## 目录详细说明

### 1. src/app/ - 应用路由

使用Next.js 15的App Router，文件系统路由：

#### 路由组 (Route Groups)
- `(main)/`: 主要功能页面，使用括号创建路由组
- 包含企业信息、论坛、用户中心等核心功能

#### 动态路由
- `companies/[id]/`: 企业详情页，使用动态路由参数
- `forum/[id]/`: 帖子详情页

#### 功能页面
- `search/`: 全局搜索结果页面
- `companies/`: 企业信息相关页面
- `forum/`: 论坛社区功能
- `profile/`: 用户中心功能

#### 静态页面
- `help/`: 帮助中心
- `about/`: 关于我们
- `privacy/`: 隐私政策
- `terms/`: 服务条款

#### API路由
- `api/`: 后端API endpoints
- 按功能模块组织API路由

### 2. src/components/ - 组件库

#### ui/ - 基础UI组件
基于shadcn/ui的可复用组件：
- `button.tsx`: 按钮组件，支持多种变体
- `card.tsx`: 卡片组件，用于内容展示
- `input.tsx`: 输入框组件
- `dialog.tsx`: 模态对话框

#### layout/ - 布局组件
页面布局相关组件：
- `header.tsx`: 页面头部，包含导航和用户信息
- `footer.tsx`: 页面脚部
- `navigation.tsx`: 主导航组件

#### features/ - 功能模块组件
按功能模块组织的业务组件：
- `companies/`: 企业相关组件
- `forum/`: 论坛相关组件
- `users/`: 用户相关组件

### 3. src/lib/ - 工具库

#### 核心配置
- `auth.ts`: NextAuth.js配置，定义认证策略和回调
- `prisma.ts`: Prisma客户端配置，数据库连接
- `utils.ts`: 通用工具函数

#### Supabase客户端
- `supabase/client.ts`: 浏览器端客户端
- `supabase/server.ts`: 服务端客户端

### 4. src/types/ - 类型定义

TypeScript类型定义，按功能模块分组：
- `database.ts`: 数据库相关类型
- `work-experience.ts`: 工作经验相关类型

### 5. prisma/ - 数据库配置

- `schema.prisma`: 数据库模式定义
- `seed.ts`: 初始数据种子文件

## 开发规范

### 1. 命名规范

#### 文件命名
- 组件文件：使用kebab-case，如`user-profile.tsx`
- 页面文件：使用page.tsx、layout.tsx等Next.js约定
- 工具文件：使用kebab-case，如`auth-utils.ts`

#### 组件命名
- React组件：使用PascalCase，如`UserProfile`
- Hook：使用camelCase，以use开头，如`useAuth`
- 常量：使用UPPER_SNAKE_CASE，如`DEFAULT_PAGE_SIZE`

### 2. 文件组织

#### 组件结构
```
components/
├── ui/           # 通用UI组件
├── layout/       # 布局组件
└── features/     # 业务组件
```

#### API结构
```
api/
├── auth/         # 认证相关
├── companies/    # 企业相关
├── posts/        # 帖子相关
└── search/       # 搜索相关
```

### 3. 代码质量

- 使用TypeScript进行类型检查
- 遵循ESLint规则
- 使用Prettier格式化代码
- 编写清晰的注释和文档

## 部署配置

### Vercel部署

项目配置为Vercel部署，支持：
- 自动构建和部署
- 环境变量管理
- 数据库集成
- 边缘函数优化

## 开发流程

### 1. 本地开发环境搭建

```bash
# 克隆项目
git clone <repository-url>
cd workmates

# 安装依赖
npm install

# 配置环境变量
cp env.example .env.local

# 初始化数据库
npm run db:generate

# 启动开发服务器
npm run dev
```

### 2. 开发工作流

1. 创建功能分支
2. 编写代码和测试
3. 运行lint和类型检查
4. 提交PR进行代码审查
5. 合并到主分支
6. 自动部署到生产环境

这个项目结构遵循Next.js 15的最佳实践，具有良好的可维护性和扩展性，适合团队开发和长期维护。
