/**
 * 工作经历相关的类型定义
 */

// 工作类型枚举
export type EmploymentType =
  | 'FULL_TIME'
  | 'PART_TIME'
  | 'CONTRACT'
  | 'INTERNSHIP'

// 审核状态枚举
export type VerificationStatus = 'PENDING' | 'APPROVED' | 'REJECTED' | 'REVOKED'

// 文件类别枚举
export type FileCategory =
  | 'CONTRACT'
  | 'CERTIFICATE'
  | 'PHOTO'
  | 'DOCUMENT'
  | 'OTHER'

// 审核决定枚举
export type VerificationDecision =
  | 'APPROVED'
  | 'REJECTED'
  | 'REVOKED'
  | 'PENDING_MORE_INFO'

// 审核目标类型枚举
export type VerificationTargetType =
  | 'WORK_EXPERIENCE'
  | 'EXPERIENCE_FILE'
  | 'SALARY'
  | 'INTERVIEW'

// 工作经历接口
export interface WorkExperience {
  id: string
  userId: string

  // 基本信息
  companyName: string
  position: string
  department?: string
  employmentType: EmploymentType

  // 时间信息
  startDate: Date
  endDate?: Date
  isCurrent: boolean

  // 工作内容
  description?: string
  responsibilities?: string[]
  achievements?: string[]
  skillsUsed?: string[]

  // 薪资信息（可选）
  salaryRange?: string
  salaryCurrency: string

  // 联系信息（可选）
  supervisorName?: string
  supervisorContact?: string

  // 审核状态
  verificationStatus: VerificationStatus
  verificationScore: number

  // 元数据
  createdAt: Date
  updatedAt: Date
  verifiedAt?: Date
  verifiedBy?: string

  // 关联数据
  files?: ExperienceFile[]
  verificationRecords?: VerificationRecord[]
}

// 工作经历文件接口
export interface ExperienceFile {
  id: string
  workExperienceId: string

  // 文件信息
  fileName: string
  fileOriginalName: string
  filePath: string
  fileSize: number
  fileType: string
  fileCategory: FileCategory

  // 文件描述
  title?: string
  description?: string

  // 审核状态
  verificationStatus: VerificationStatus
  isPublic: boolean

  // 元数据
  uploadedAt: Date
  verifiedAt?: Date
  verifiedBy?: string
}

// 审核记录接口
export interface VerificationRecord {
  id: string

  // 审核对象
  targetType: VerificationTargetType
  targetId: string

  // 审核信息
  reviewerId: string
  previousStatus?: VerificationStatus
  newStatus: VerificationStatus

  // 审核结果
  decision: VerificationDecision
  confidenceLevel: number

  // 审核详情
  reviewNotes?: string
  issuesFound?: string[]
  requiredActions?: string[]

  // 时间信息
  createdAt: Date

  // 关联数据
  reviewer?: {
    id: string
    name: string
    username: string
  }
}

// 用户信誉接口
export interface UserCredibility {
  id: string
  userId: string

  // 信誉分数
  overallScore: number
  workExperienceScore: number
  salaryContributionScore: number
  interviewContributionScore: number

  // 验证统计
  verifiedExperiencesCount: number
  verifiedFilesCount: number
  verifiedSalariesCount: number
  verifiedInterviewsCount: number

  // 贡献统计
  totalContributionsCount: number
  helpfulContributionsCount: number
  flaggedContributionsCount: number

  // 时间信息
  createdAt: Date
  updatedAt: Date
  lastCalculatedAt: Date
}

// 用户工作经历汇总接口
export interface UserWorkExperienceSummary {
  userId: string
  username: string
  name: string
  totalExperiences: number
  verifiedExperiences: number
  pendingExperiences: number
  rejectedExperiences: number
  totalFiles: number
  verifiedFiles: number
  credibilityScore: number
  workExperienceScore: number
}

// 待审核内容接口
export interface PendingVerification {
  contentType: VerificationTargetType
  contentId: string
  userId: string
  username: string
  userName: string
  companyName: string
  position: string
  createdAt: Date
  verificationStatus: VerificationStatus
  attachedFiles: number
}

// 表单数据接口
export interface WorkExperienceFormData {
  companyName: string
  position: string
  department?: string
  employmentType: EmploymentType
  startDate: Date
  endDate?: Date
  isCurrent: boolean
  description?: string
  responsibilities?: string[]
  achievements?: string[]
  skillsUsed?: string[]
  salaryRange?: string
  supervisorName?: string
  supervisorContact?: string
}

// 文件上传表单数据接口
export interface FileUploadFormData {
  file: File
  title?: string
  description?: string
  category: FileCategory
}

// API 响应接口
export interface WorkExperienceResponse {
  success: boolean
  data?: WorkExperience
  error?: string
}

export interface WorkExperienceListResponse {
  success: boolean
  data?: WorkExperience[]
  total?: number
  page?: number
  limit?: number
  error?: string
}

export interface FileUploadResponse {
  success: boolean
  data?: ExperienceFile
  error?: string
}

export interface VerificationResponse {
  success: boolean
  data?: VerificationRecord
  error?: string
}

export interface CredibilityResponse {
  success: boolean
  data?: UserCredibility
  error?: string
}

// 查询参数接口
export interface WorkExperienceQueryParams {
  page?: number
  limit?: number
  status?: VerificationStatus
  companyName?: string
  position?: string
  userId?: string
}

// 验证状态显示配置
export interface VerificationStatusConfig {
  status: VerificationStatus
  label: string
  color: string
  icon: string
  description: string
}

// 文件类别显示配置
export interface FileCategoryConfig {
  category: FileCategory
  label: string
  icon: string
  description: string
  acceptedTypes: string[]
}

// 工作类型显示配置
export interface EmploymentTypeConfig {
  type: EmploymentType
  label: string
  description: string
}

// 信誉等级配置
export interface CredibilityLevelConfig {
  minScore: number
  maxScore: number
  level: string
  label: string
  color: string
  icon: string
  description: string
}

// 导出常量配置
export const EMPLOYMENT_TYPE_CONFIG: Record<
  EmploymentType,
  EmploymentTypeConfig
> = {
  FULL_TIME: {
    type: 'FULL_TIME',
    label: '全职',
    description: '全职工作',
  },
  PART_TIME: {
    type: 'PART_TIME',
    label: '兼职',
    description: '兼职工作',
  },
  CONTRACT: {
    type: 'CONTRACT',
    label: '合同工',
    description: '合同制工作',
  },
  INTERNSHIP: {
    type: 'INTERNSHIP',
    label: '实习',
    description: '实习工作',
  },
}

export const VERIFICATION_STATUS_CONFIG: Record<
  VerificationStatus,
  VerificationStatusConfig
> = {
  PENDING: {
    status: 'PENDING',
    label: '待审核',
    color: 'yellow',
    icon: 'Clock',
    description: '等待管理员审核',
  },
  APPROVED: {
    status: 'APPROVED',
    label: '已通过',
    color: 'green',
    icon: 'CheckCircle',
    description: '审核通过',
  },
  REJECTED: {
    status: 'REJECTED',
    label: '已拒绝',
    color: 'red',
    icon: 'XCircle',
    description: '审核被拒绝',
  },
  REVOKED: {
    status: 'REVOKED',
    label: '已撤销',
    color: 'gray',
    icon: 'Ban',
    description: '审核结果被撤销',
  },
}

export const FILE_CATEGORY_CONFIG: Record<FileCategory, FileCategoryConfig> = {
  CONTRACT: {
    category: 'CONTRACT',
    label: '劳动合同',
    icon: 'FileText',
    description: '劳动合同或聘用协议',
    acceptedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
  },
  CERTIFICATE: {
    category: 'CERTIFICATE',
    label: '工作证明',
    icon: 'Award',
    description: '工作证明或在职证明',
    acceptedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
  },
  PHOTO: {
    category: 'PHOTO',
    label: '工作照片',
    icon: 'Camera',
    description: '工作场所或活动照片',
    acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  },
  DOCUMENT: {
    category: 'DOCUMENT',
    label: '其他文档',
    icon: 'File',
    description: '其他相关文档',
    acceptedTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
  },
  OTHER: {
    category: 'OTHER',
    label: '其他',
    icon: 'Paperclip',
    description: '其他类型文件',
    acceptedTypes: ['*'],
  },
}

export const CREDIBILITY_LEVEL_CONFIG: CredibilityLevelConfig[] = [
  {
    minScore: 0,
    maxScore: 0.2,
    level: 'NEWBIE',
    label: '新手',
    color: 'gray',
    icon: 'User',
    description: '刚开始使用平台，信誉度较低',
  },
  {
    minScore: 0.2,
    maxScore: 0.4,
    level: 'BASIC',
    label: '基础',
    color: 'blue',
    icon: 'UserCheck',
    description: '有一定贡献，信誉度一般',
  },
  {
    minScore: 0.4,
    maxScore: 0.6,
    level: 'VERIFIED',
    label: '已认证',
    color: 'green',
    icon: 'Shield',
    description: '信息较为可靠，信誉度良好',
  },
  {
    minScore: 0.6,
    maxScore: 0.8,
    level: 'TRUSTED',
    label: '可信',
    color: 'purple',
    icon: 'ShieldCheck',
    description: '高度可信，信誉度很高',
  },
  {
    minScore: 0.8,
    maxScore: 1.0,
    level: 'EXPERT',
    label: '专家',
    color: 'gold',
    icon: 'Star',
    description: '平台专家，信誉度极高',
  },
]

// 工具函数
export const getVerificationStatusConfig = (
  status: VerificationStatus
): VerificationStatusConfig => {
  return VERIFICATION_STATUS_CONFIG[status]
}

export const getFileCategoryConfig = (
  category: FileCategory
): FileCategoryConfig => {
  return FILE_CATEGORY_CONFIG[category]
}

export const getEmploymentTypeConfig = (
  type: EmploymentType
): EmploymentTypeConfig => {
  return EMPLOYMENT_TYPE_CONFIG[type]
}

export const getCredibilityLevelConfig = (
  score: number
): CredibilityLevelConfig => {
  return (
    CREDIBILITY_LEVEL_CONFIG.find(
      config => score >= config.minScore && score <= config.maxScore
    ) || CREDIBILITY_LEVEL_CONFIG[0]
  )
}

export const formatDateRange = (
  startDate: Date,
  endDate?: Date,
  isCurrent?: boolean
): string => {
  const start = startDate.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
  })

  if (isCurrent) {
    return `${start} - 至今`
  }

  if (endDate) {
    const end = endDate.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
    })
    return `${start} - ${end}`
  }

  return start
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const calculateExperienceDuration = (
  startDate: Date,
  endDate?: Date
): string => {
  const end = endDate || new Date()
  const diffInMonths =
    (end.getFullYear() - startDate.getFullYear()) * 12 +
    (end.getMonth() - startDate.getMonth())

  if (diffInMonths < 1) {
    return '不足1个月'
  }

  const years = Math.floor(diffInMonths / 12)
  const months = diffInMonths % 12

  let result = ''
  if (years > 0) {
    result += `${years}年`
  }
  if (months > 0) {
    result += `${months}个月`
  }

  return result
}
