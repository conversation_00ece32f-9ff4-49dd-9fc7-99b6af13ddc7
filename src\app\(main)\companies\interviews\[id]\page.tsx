import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    ArrowLeft,
    Award,
    Bookmark,
    Building2,
    Calendar,
    Clock,
    MapPin,
    MessageSquare,
    Share2,
    Star,
    Target,
    ThumbsDown,
    ThumbsUp,
    TrendingUp,
    Users
} from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { Suspense } from 'react'

interface InterviewDetailPageProps {
  params: {
    id: string
  }
}

/**
 * 面经详情页面
 * 展示具体面试经验的详细信息
 */
export default function InterviewDetailPage({ params }: InterviewDetailPageProps) {
  const interviewId = params.id

  return (
    <div className="container mx-auto px-4 py-8">
      <Suspense fallback={<InterviewDetailSkeleton />}>
        <InterviewDetailContent interviewId={interviewId} />
      </Suspense>
    </div>
  )
}

/**
 * 面经详情内容组件
 */
function InterviewDetailContent({ interviewId }: { interviewId: string }) {
  // 模拟数据获取
  const interview = getInterviewById(interviewId)

  if (!interview) {
    notFound()
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* 返回按钮 */}
      <Link href="/companies/interviews">
        <Button variant="ghost" className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回面经列表
        </Button>
      </Link>

      {/* 面经标题和基本信息 */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div className="space-y-4">
              <h1 className="text-3xl font-bold">{interview.title}</h1>
              
              {/* 企业和职位信息 */}
              <div className="flex items-center gap-4 text-gray-600">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  <span className="font-medium">{interview.company}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  <span>{interview.position}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>{interview.location}</span>
                </div>
              </div>

              {/* 标签 */}
              <div className="flex flex-wrap gap-2">
                {interview.tags.map(tag => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
                <Badge 
                  variant={interview.result === 'offer' ? 'default' : 'destructive'}
                >
                  {getResultText(interview.result)}
                </Badge>
                <Badge variant="outline">
                  难度: {getDifficultyText(interview.difficulty)}
                </Badge>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Bookmark className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 面试概览 */}
          <div className="grid gap-4 md:grid-cols-4 mb-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Users className="h-6 w-6 mx-auto mb-2 text-blue-600" />
              <p className="text-sm text-gray-600">面试轮数</p>
              <p className="font-bold">{interview.rounds}轮</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Clock className="h-6 w-6 mx-auto mb-2 text-green-600" />
              <p className="text-sm text-gray-600">流程时长</p>
              <p className="font-bold">{interview.processTime}</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <Star className="h-6 w-6 mx-auto mb-2 text-yellow-600" />
              <p className="text-sm text-gray-600">整体评价</p>
              <p className="font-bold">{interview.rating}/5</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <TrendingUp className="h-6 w-6 mx-auto mb-2 text-purple-600" />
              <p className="text-sm text-gray-600">推荐指数</p>
              <p className="font-bold">{interview.recommendation}/5</p>
            </div>
          </div>

          {/* 作者信息 */}
          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <Avatar>
              <AvatarImage src={interview.author.avatar} />
              <AvatarFallback>{interview.author.name[0]}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">{interview.author.name}</span>
                {interview.author.verified && (
                  <Award className="h-4 w-4 text-blue-600" />
                )}
              </div>
              <p className="text-sm text-gray-600">
                {interview.author.experience} • {interview.author.background}
              </p>
            </div>
            <div className="text-right text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {interview.submittedAt}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 面试详情 */}
      <Card>
        <CardHeader>
          <CardTitle>面试详情</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 面试流程 */}
          {interview.rounds_detail.map((round, index) => (
            <div key={index} className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-sm">
                  {index + 1}
                </div>
                <h3 className="text-lg font-semibold">{round.title}</h3>
                <Badge variant="outline">{round.duration}</Badge>
              </div>
              
              <div className="ml-10 space-y-4">
                {/* 面试官信息 */}
                {round.interviewer && (
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">面试官：</span>
                    {round.interviewer}
                  </div>
                )}

                {/* 面试内容 */}
                <div>
                  <h4 className="font-medium mb-2">面试内容：</h4>
                  <p className="text-gray-700 whitespace-pre-line">
                    {round.content}
                  </p>
                </div>

                {/* 面试问题 */}
                {round.questions && round.questions.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">主要问题：</h4>
                    <ul className="space-y-2">
                      {round.questions.map((question, qIndex) => (
                        <li key={qIndex} className="flex items-start gap-2">
                          <span className="text-blue-600 mt-1">•</span>
                          <span className="text-gray-700">{question}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* 建议和感受 */}
                {round.feedback && (
                  <div>
                    <h4 className="font-medium mb-2">个人感受：</h4>
                    <p className="text-gray-700 whitespace-pre-line">
                      {round.feedback}
                    </p>
                  </div>
                )}
              </div>
              
              {index < interview.rounds_detail.length - 1 && (
                <div className="my-6" />
              )}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* 总结和建议 */}
      <Card>
        <CardHeader>
          <CardTitle>总结和建议</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">整体感受：</h4>
            <p className="text-gray-700 whitespace-pre-line">
              {interview.summary}
            </p>
          </div>

          {interview.advice && (
            <div>
              <h4 className="font-medium mb-2">给后来者的建议：</h4>
              <p className="text-gray-700 whitespace-pre-line">
                {interview.advice}
              </p>
            </div>
          )}

          {interview.preparation && (
            <div>
              <h4 className="font-medium mb-2">面试准备：</h4>
              <p className="text-gray-700 whitespace-pre-line">
                {interview.preparation}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 互动区域 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <Button variant="ghost" size="sm">
                <ThumbsUp className="mr-2 h-4 w-4" />
                有用 ({interview.likes})
              </Button>
              <Button variant="ghost" size="sm">
                <ThumbsDown className="mr-2 h-4 w-4" />
                无用 ({interview.dislikes})
              </Button>
              <Button variant="ghost" size="sm">
                <MessageSquare className="mr-2 h-4 w-4" />
                评论 ({interview.comments})
              </Button>
            </div>
            <div className="text-sm text-gray-500">
              浏览量: {interview.views}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 相关面经推荐 */}
      <Card>
        <CardHeader>
          <CardTitle>相关面经推荐</CardTitle>
        </CardHeader>
        <CardContent>
          <RelatedInterviews currentId={interviewId} />
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * 相关面经推荐组件
 */
function RelatedInterviews({ currentId }: { currentId: string }) {
  // 模拟相关面经数据
  const relatedInterviews = [
    {
      id: '2',
      title: '阿里巴巴算法工程师面试经验',
      position: '算法工程师',
      result: 'offer',
      difficulty: 'hard',
      likes: 89,
      comments: 12,
      submittedAt: '2024-01-15'
    },
    {
      id: '3',
      title: '阿里巴巴产品经理面试分享',
      position: '产品经理',
      result: 'reject',
      difficulty: 'medium',
      likes: 56,
      comments: 8,
      submittedAt: '2024-01-10'
    }
  ].filter(item => item.id !== currentId)

  return (
    <div className="space-y-4">
      {relatedInterviews.map(item => (
        <Link key={item.id} href={`/companies/interviews/${item.id}`}>
          <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h4 className="font-medium mb-1">{item.title}</h4>
                <p className="text-sm text-gray-600 mb-2">{item.position}</p>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    <ThumbsUp className="h-3 w-3" />
                    {item.likes}
                  </span>
                  <span className="flex items-center gap-1">
                    <MessageSquare className="h-3 w-3" />
                    {item.comments}
                  </span>
                  <span>{item.submittedAt}</span>
                </div>
              </div>
              <div className="flex flex-col items-end gap-2">
                <Badge 
                  variant={item.result === 'offer' ? 'default' : 'destructive'}
                  className="text-xs"
                >
                  {getResultText(item.result)}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {getDifficultyText(item.difficulty)}
                </Badge>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}

/**
 * 加载骨架屏
 */
function InterviewDetailSkeleton() {
  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="h-8 bg-gray-200 rounded animate-pulse" />
      <div className="space-y-4">
        <div className="h-32 bg-gray-200 rounded animate-pulse" />
        <div className="h-64 bg-gray-200 rounded animate-pulse" />
        <div className="h-48 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  )
}

/**
 * 模拟数据获取函数
 */
function getInterviewById(id: string) {
  const interviews = {
    '1': {
      id: '1',
      title: '阿里巴巴前端工程师面试经验 - 三轮技术面试详细分享',
      company: '阿里巴巴',
      position: '前端工程师',
      location: '杭州',
      result: 'offer',
      difficulty: 'medium',
      tags: ['前端', 'React', '算法', '系统设计'],
      rounds: 4,
      processTime: '2周',
      rating: 4.5,
      recommendation: 4.0,
      author: {
        name: '匿名前端工程师',
        avatar: '/avatar1.jpg',
        experience: '5年经验',
        background: '985本科 + 3年大厂经验',
        verified: true
      },
      submittedAt: '2024-01-20',
      likes: 156,
      dislikes: 8,
      comments: 23,
      views: 2340,
      summary: '整个面试过程比较正规，面试官都很专业。技术深度要求比较高，但是面试氛围很好。HR面试主要关注个人发展规划和团队协作能力。',
      advice: '建议提前准备算法题，特别是动态规划和树相关的题目。项目经验要能深入讲解，包括技术选型的原因和遇到的困难。',
      preparation: '准备了2个月，主要刷LeetCode中等难度题目，复习了React源码，准备了3个项目的详细介绍。',
      rounds_detail: [
        {
          title: '一面 - 基础技术面试',
          duration: '60分钟',
          interviewer: '团队技术负责人',
          content: '主要考察JavaScript基础、React使用、CSS布局、HTTP协议等基础知识。还有2道算法题：数组去重和二叉树遍历。',
          questions: [
            'JavaScript的事件循环机制',
            'React的Virtual DOM原理',
            'CSS实现垂直居中的方法',
            '算法题：实现数组去重函数',
            '算法题：二叉树的中序遍历'
          ],
          feedback: '面试官很友善，会引导思路。算法题不算太难，主要考察基础功底。'
        },
        {
          title: '二面 - 项目经验面试',
          duration: '45分钟',
          interviewer: '资深前端工程师',
          content: '深入讨论项目经验，包括技术难点、性能优化、团队协作等。还讨论了前端工程化相关问题。',
          questions: [
            '介绍你最有挑战性的项目',
            '如何进行前端性能优化',
            'Webpack的打包原理',
            '如何处理跨域问题',
            '团队协作中遇到的问题'
          ],
          feedback: '面试官对项目细节问得很深，需要对自己的项目非常熟悉。建议准备具体的数据和案例。'
        },
        {
          title: '三面 - 系统设计面试',
          duration: '50分钟',
          interviewer: '前端架构师',
          content: '设计一个类似淘宝首页的前端系统，考虑性能、可维护性、扩展性等因素。还讨论了微前端架构。',
          questions: [
            '设计一个大型电商首页的前端架构',
            '如何处理大量商品数据的渲染',
            '微前端的优缺点',
            '前端监控和错误处理',
            '如何设计组件库'
          ],
          feedback: '这轮最有挑战性，需要考虑很多实际的工程问题。面试官会不断提出新的需求来考验设计的扩展性。'
        },
        {
          title: '四面 - HR面试',
          duration: '30分钟',
          interviewer: 'HR经理',
          content: '主要了解个人背景、职业规划、薪资期望等。也会问一些行为面试题。',
          questions: [
            '为什么选择阿里巴巴',
            '你的职业规划是什么',
            '如何处理工作压力',
            '描述一次团队冲突的处理',
            '对加班的看法'
          ],
          feedback: 'HR面试比较轻松，主要是相互了解。要诚实表达自己的想法和期望。'
        }
      ]
    }
  }

  return interviews[id as keyof typeof interviews] || null
}

/**
 * 获取面试结果文本
 */
function getResultText(result: string): string {
  const resultMap = {
    offer: '获得Offer',
    reject: '被拒绝',
    pending: '流程中',
    withdraw: '主动放弃'
  }
  return resultMap[result as keyof typeof resultMap] || result
}

/**
 * 获取面试难度文本
 */
function getDifficultyText(difficulty: string): string {
  const difficultyMap = {
    easy: '简单',
    medium: '中等',
    hard: '困难',
    'very-hard': '很困难'
  }
  return difficultyMap[difficulty as keyof typeof difficultyMap] || difficulty
} 