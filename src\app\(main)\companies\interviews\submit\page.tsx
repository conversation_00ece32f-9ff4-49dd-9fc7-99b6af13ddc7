'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
    ArrowLeft,
    Building2,
    Clock,
    FileText,
    Plus,
    Shield,
    Trash2
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

interface InterviewRound {
  id: string
  title: string
  duration: string
  interviewer: string
  content: string
  questions: string[]
  feedback: string
}

/**
 * 面经提交页面
 * 允许用户分享面试经验
 */
export default function InterviewSubmitPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  
  const [formData, setFormData] = useState({
    // 基本信息
    title: '',
    company: '',
    position: '',
    location: '',
    department: '',
    level: '',
    
    // 面试结果
    result: '',
    difficulty: '',
    rating: '',
    recommendation: '',
    processTime: '',
    
    // 面试轮次
    rounds: [] as InterviewRound[],
    
    // 总结
    summary: '',
    advice: '',
    preparation: '',
    
    // 其他信息
    tags: [] as string[],
    isAnonymous: true,
    agreeTerms: false,
  })

  const handleInputChange = (field: string, value: string | boolean | string[] | InterviewRound[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleTagToggle = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }))
  }

  const addRound = () => {
    const newRound: InterviewRound = {
      id: Date.now().toString(),
      title: '',
      duration: '',
      interviewer: '',
      content: '',
      questions: [''],
      feedback: ''
    }
    setFormData(prev => ({
      ...prev,
      rounds: [...prev.rounds, newRound]
    }))
  }

  const removeRound = (roundId: string) => {
    setFormData(prev => ({
      ...prev,
      rounds: prev.rounds.filter(r => r.id !== roundId)
    }))
  }

  const updateRound = (roundId: string, field: string, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      rounds: prev.rounds.map(round =>
        round.id === roundId ? { ...round, [field]: value } : round
      )
    }))
  }

  const addQuestion = (roundId: string) => {
    setFormData(prev => ({
      ...prev,
      rounds: prev.rounds.map(round =>
        round.id === roundId 
          ? { ...round, questions: [...round.questions, ''] }
          : round
      )
    }))
  }

  const updateQuestion = (roundId: string, questionIndex: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      rounds: prev.rounds.map(round =>
        round.id === roundId
          ? {
              ...round,
              questions: round.questions.map((q, i) => 
                i === questionIndex ? value : q
              )
            }
          : round
      )
    }))
  }

  const removeQuestion = (roundId: string, questionIndex: number) => {
    setFormData(prev => ({
      ...prev,
      rounds: prev.rounds.map(round =>
        round.id === roundId
          ? {
              ...round,
              questions: round.questions.filter((_, i) => i !== questionIndex)
            }
          : round
      )
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.agreeTerms) {
      alert('请同意分享条款')
      return
    }

    setIsSubmitting(true)
    try {
      // 调用面试经验提交API
      const response = await fetch('/api/interviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          company: formData.company,
          position: formData.position,
          location: formData.location,
          department: formData.department,
          level: formData.level,
          result: formData.result,
          difficulty: formData.difficulty,
          rating: formData.rating,
          recommendation: formData.recommendation,
          processTime: formData.processTime,
          rounds: formData.rounds,
          summary: formData.summary,
          advice: formData.advice,
          preparation: formData.preparation,
          tags: formData.tags,
          isAnonymous: formData.isAnonymous,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || '提交失败')
      }

      alert('面经分享成功！感谢您的贡献')
      router.push('/companies/interviews')
    } catch (error) {
      console.error('提交面经失败:', error)
      alert(error instanceof Error ? error.message : '提交失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  const predefinedTags = [
    '算法', '系统设计', '前端', '后端', '全栈', '移动端',
    '数据库', '网络', '操作系统', '项目经验',
    'React', 'Vue', 'Angular', 'Node.js', 'Python', 'Java',
    'Go', 'C++', 'iOS', 'Android', '机器学习', 'AI'
  ]

  const steps = [
    { id: 1, title: '基本信息', icon: Building2 },
    { id: 2, title: '面试详情', icon: Clock },
    { id: 3, title: '总结建议', icon: FileText },
  ]

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 页面标题 */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <h1 className="text-3xl font-bold mb-2">分享面经</h1>
        <p className="text-gray-600">
          分享您的面试经验，帮助其他求职者做好准备
        </p>
      </div>

      {/* 步骤指示器 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                currentStep >= step.id 
                  ? 'bg-blue-600 border-blue-600 text-white' 
                  : 'border-gray-300 text-gray-300'
              }`}>
                <step.icon className="w-5 h-5" />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
              }`}>
                {step.title}
              </span>
              {index < steps.length - 1 && (
                <div className={`w-16 h-0.5 mx-4 ${
                  currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 匿名提示 */}
      <Alert className="mb-8">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          我们支持匿名分享面经。您的个人信息将被保护，只有面试经验会被公开展示。
        </AlertDescription>
      </Alert>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 第一步：基本信息 */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">面经标题 *</Label>
                <Input
                  id="title"
                  placeholder="如：阿里巴巴前端工程师面试经验"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  required
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="company">公司名称 *</Label>
                  <Input
                    id="company"
                    placeholder="请输入公司名称"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="position">职位名称 *</Label>
                  <Input
                    id="position"
                    placeholder="如：前端工程师"
                    value={formData.position}
                    onChange={(e) => handleInputChange('position', e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="location">工作地点 *</Label>
                  <Select value={formData.location} onValueChange={(value) => handleInputChange('location', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择城市" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beijing">北京</SelectItem>
                      <SelectItem value="shanghai">上海</SelectItem>
                      <SelectItem value="guangzhou">广州</SelectItem>
                      <SelectItem value="shenzhen">深圳</SelectItem>
                      <SelectItem value="hangzhou">杭州</SelectItem>
                      <SelectItem value="chengdu">成都</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">部门</Label>
                  <Input
                    id="department"
                    placeholder="如：技术部"
                    value={formData.department}
                    onChange={(e) => handleInputChange('department', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="level">职级</Label>
                  <Input
                    id="level"
                    placeholder="如：P6, T3-2"
                    value={formData.level}
                    onChange={(e) => handleInputChange('level', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="result">面试结果 *</Label>
                  <Select value={formData.result} onValueChange={(value) => handleInputChange('result', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择面试结果" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="offer">获得Offer</SelectItem>
                      <SelectItem value="reject">被拒绝</SelectItem>
                      <SelectItem value="pending">流程中</SelectItem>
                      <SelectItem value="withdraw">主动放弃</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="difficulty">面试难度 *</Label>
                  <Select value={formData.difficulty} onValueChange={(value) => handleInputChange('difficulty', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择面试难度" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">简单</SelectItem>
                      <SelectItem value="medium">中等</SelectItem>
                      <SelectItem value="hard">困难</SelectItem>
                      <SelectItem value="very-hard">很困难</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="rating">整体评价 (1-5)</Label>
                  <Select value={formData.rating} onValueChange={(value) => handleInputChange('rating', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="评分" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1分</SelectItem>
                      <SelectItem value="2">2分</SelectItem>
                      <SelectItem value="3">3分</SelectItem>
                      <SelectItem value="4">4分</SelectItem>
                      <SelectItem value="5">5分</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="recommendation">推荐指数 (1-5)</Label>
                  <Select value={formData.recommendation} onValueChange={(value) => handleInputChange('recommendation', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="推荐指数" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1分</SelectItem>
                      <SelectItem value="2">2分</SelectItem>
                      <SelectItem value="3">3分</SelectItem>
                      <SelectItem value="4">4分</SelectItem>
                      <SelectItem value="5">5分</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="processTime">流程时长</Label>
                  <Input
                    id="processTime"
                    placeholder="如：2周"
                    value={formData.processTime}
                    onChange={(e) => handleInputChange('processTime', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label>相关标签 (可多选)</Label>
                <div className="flex flex-wrap gap-2">
                  {predefinedTags.map(tag => (
                    <Badge
                      key={tag}
                      variant={formData.tags.includes(tag) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => handleTagToggle(tag)}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex justify-end">
                <Button 
                  type="button" 
                  onClick={() => setCurrentStep(2)}
                  disabled={!formData.company || !formData.position || !formData.location || !formData.result || !formData.difficulty}
                >
                  下一步
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 第二步：面试详情 */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                面试详情
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {formData.rounds.map((round, index) => (
                <Card key={round.id} className="relative">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">
                        第 {index + 1} 轮面试
                      </CardTitle>
                      {formData.rounds.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeRound(round.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="space-y-2">
                        <Label>面试轮次名称</Label>
                        <Input
                          placeholder="如：技术面试"
                          value={round.title}
                          onChange={(e) => updateRound(round.id, 'title', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>面试时长</Label>
                        <Input
                          placeholder="如：60分钟"
                          value={round.duration}
                          onChange={(e) => updateRound(round.id, 'duration', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>面试官</Label>
                        <Input
                          placeholder="如：技术负责人"
                          value={round.interviewer}
                          onChange={(e) => updateRound(round.id, 'interviewer', e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>面试内容和过程</Label>
                      <Textarea
                        placeholder="描述面试的整体过程、考察内容等..."
                        value={round.content}
                        onChange={(e) => updateRound(round.id, 'content', e.target.value)}
                        rows={4}
                      />
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label>面试问题</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addQuestion(round.id)}
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          添加问题
                        </Button>
                      </div>
                      {round.questions.map((question, qIndex) => (
                        <div key={qIndex} className="flex gap-2">
                          <Input
                            placeholder={`问题 ${qIndex + 1}`}
                            value={question}
                            onChange={(e) => updateQuestion(round.id, qIndex, e.target.value)}
                            className="flex-1"
                          />
                          {round.questions.length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeQuestion(round.id, qIndex)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>

                    <div className="space-y-2">
                      <Label>个人感受和建议</Label>
                      <Textarea
                        placeholder="分享您对这轮面试的感受、建议等..."
                        value={round.feedback}
                        onChange={(e) => updateRound(round.id, 'feedback', e.target.value)}
                        rows={3}
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}

              <div className="flex justify-center">
                <Button
                  type="button"
                  variant="outline"
                  onClick={addRound}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  添加面试轮次
                </Button>
              </div>

              <div className="flex justify-between">
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setCurrentStep(1)}
                >
                  上一步
                </Button>
                <Button 
                  type="button" 
                  onClick={() => setCurrentStep(3)}
                >
                  下一步
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 第三步：总结建议 */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                总结和建议
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="summary">整体感受 *</Label>
                <Textarea
                  id="summary"
                  placeholder="请分享您对整个面试过程的整体感受..."
                  value={formData.summary}
                  onChange={(e) => handleInputChange('summary', e.target.value)}
                  rows={4}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="advice">给后来者的建议</Label>
                <Textarea
                  id="advice"
                  placeholder="给后续面试者的建议和提醒..."
                  value={formData.advice}
                  onChange={(e) => handleInputChange('advice', e.target.value)}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="preparation">面试准备</Label>
                <Textarea
                  id="preparation"
                  placeholder="您是如何准备这次面试的..."
                  value={formData.preparation}
                  onChange={(e) => handleInputChange('preparation', e.target.value)}
                  rows={4}
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isAnonymous"
                    checked={formData.isAnonymous}
                    onCheckedChange={(checked) => handleInputChange('isAnonymous', checked)}
                  />
                  <Label htmlFor="isAnonymous">
                    匿名分享（推荐）
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="agreeTerms"
                    checked={formData.agreeTerms}
                    onCheckedChange={(checked) => handleInputChange('agreeTerms', checked)}
                  />
                  <Label htmlFor="agreeTerms" className="text-sm">
                    我同意分享条款，了解提交的面经将公开展示帮助其他求职者
                  </Label>
                </div>
              </div>

              <div className="flex justify-between">
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setCurrentStep(2)}
                >
                  上一步
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || !formData.agreeTerms || !formData.summary}
                >
                  {isSubmitting ? '提交中...' : '提交面经'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </form>
    </div>
  )
} 