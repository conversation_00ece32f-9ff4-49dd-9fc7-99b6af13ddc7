'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Building2,
  Filter,
  MapPin,
  Plus,
  Search,
  Star,
  TrendingUp,
  Users,
} from 'lucide-react'
import Link from 'next/link'
import { useCallback, useEffect, useState } from 'react'

// 公司规模映射
const companySizeMap = {
  STARTUP: '初创期 (1-50人)',
  SMALL: '小型 (51-200人)',
  MEDIUM: '中型 (201-500人)',
  LARGE: '大型 (501-1000人)',
  ENTERPRISE: '企业级 (1000人以上)',
}

// 行业选项
const industryOptions = [
  { value: 'internet', label: '互联网' },
  { value: 'finance', label: '金融' },
  { value: 'manufacturing', label: '制造业' },
  { value: 'education', label: '教育' },
  { value: 'healthcare', label: '医疗健康' },
  { value: 'retail', label: '零售' },
  { value: 'real_estate', label: '房地产' },
  { value: 'media', label: '媒体' },
  { value: 'gaming', label: '游戏' },
  { value: 'automotive', label: '汽车' },
]

// 公司规模选项
const sizeOptions = [
  { value: 'STARTUP', label: '1-50人' },
  { value: 'SMALL', label: '51-200人' },
  { value: 'MEDIUM', label: '201-500人' },
  { value: 'LARGE', label: '501-1000人' },
  { value: 'ENTERPRISE', label: '1000人以上' },
]

interface Company {
  id: string
  name: string
  nameEn?: string
  logo?: string
  description?: string
  industry?: string
  size?: keyof typeof companySizeMap
  headquarters?: string
  isVerified: boolean
  averageRating?: number
  totalRatings: number
  totalSalaries: number
  totalReviews: number
  createdAt: string
}

interface ApiResponse {
  success: boolean
  message: string
  data: Company[]
  meta: {
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    timestamp: string
    version: string
  }
}

/**
 * 企业列表页面
 * 展示所有企业信息，支持搜索、筛选和排序
 */
export default function CompaniesPage() {
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedIndustry, setSelectedIndustry] = useState('')
  const [selectedSize, setSelectedSize] = useState('')
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)

  // 获取公司列表
  const fetchCompanies = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        sort: sortBy,
        order: sortOrder,
      })

      if (searchTerm) params.append('search', searchTerm)
      if (selectedIndustry) params.append('industry', selectedIndustry)
      if (selectedSize) params.append('size', selectedSize)

      const response = await fetch(`/api/companies?${params}`)
      const result: ApiResponse = await response.json()

      if (result.success) {
        setCompanies(result.data)
        setTotalPages(result.meta.pagination.totalPages)
        setTotalCount(result.meta.pagination.total)
      } else {
        setError(result.message || '获取公司列表失败')
      }
    } catch (err) {
      setError('网络请求失败，请稍后重试')
      console.error('获取公司列表失败:', err)
    } finally {
      setLoading(false)
    }
  }, [
    currentPage,
    searchTerm,
    selectedIndustry,
    selectedSize,
    sortBy,
    sortOrder,
  ])

  // 页面加载时获取数据
  useEffect(() => {
    fetchCompanies()
  }, [fetchCompanies])

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1) // 重置到第一页
  }

  // 筛选处理
  const handleFilterChange = (type: 'industry' | 'size', value: string) => {
    if (type === 'industry') {
      setSelectedIndustry(value)
    } else {
      setSelectedSize(value)
    }
    setCurrentPage(1) // 重置到第一页
  }

  // 清除筛选
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedIndustry('')
    setSelectedSize('')
    setCurrentPage(1)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">企业信息</h1>
            <p className="text-gray-600">
              探索真实的企业评价、薪资水平和工作环境
            </p>
          </div>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            添加企业
          </Button>
        </div>
      </div>

      {/* 搜索和筛选栏 */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
        <div className="grid gap-4 md:grid-cols-4">
          {/* 搜索框 */}
          <div className="md:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索企业名称..."
                className="pl-10"
                value={searchTerm}
                onChange={e => handleSearch(e.target.value)}
              />
            </div>
          </div>

          {/* 行业筛选 */}
          <Select
            defaultValue="全部行业"
            value={selectedIndustry}
            onValueChange={value => handleFilterChange('industry', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="选择行业" />
            </SelectTrigger>
            <SelectContent>
              {industryOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* 规模筛选 */}
          <Select
            defaultValue="全部规模"
            value={selectedSize}
            onValueChange={value => handleFilterChange('size', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="企业规模" />
            </SelectTrigger>
            <SelectContent>
              {sizeOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 高级筛选和排序 */}
        <div className="mt-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={clearFilters}>
              <Filter className="mr-2 h-4 w-4" />
              清除筛选
            </Button>
            <Select
              value={`${sortBy}-${sortOrder}`}
              onValueChange={value => {
                const [sort, order] = value.split('-')
                setSortBy(sort)
                setSortOrder(order)
              }}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">最新创建</SelectItem>
                <SelectItem value="createdAt-asc">最早创建</SelectItem>
                <SelectItem value="name-asc">名称升序</SelectItem>
                <SelectItem value="name-desc">名称降序</SelectItem>
                <SelectItem value="averageRating-desc">评分从高到低</SelectItem>
                <SelectItem value="averageRating-asc">评分从低到高</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="text-sm text-gray-600">
            找到{' '}
            <span className="font-semibold">{totalCount.toLocaleString()}</span>{' '}
            家企业
          </div>
        </div>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
          <p className="text-red-600">{error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={fetchCompanies}
          >
            重试
          </Button>
        </div>
      )}

      {/* 企业列表 */}
      {loading ? (
        <CompanyListSkeleton />
      ) : (
        <>
          <CompanyList companies={companies} />

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center gap-4 mt-8">
              <Button
                variant="outline"
                disabled={currentPage <= 1}
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              >
                上一页
              </Button>
              <span className="text-sm text-gray-600">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                disabled={currentPage >= totalPages}
                onClick={() =>
                  setCurrentPage(prev => Math.min(totalPages, prev + 1))
                }
              >
                下一页
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  )
}

/**
 * 企业列表组件
 */
function CompanyList({ companies }: { companies: Company[] }) {
  if (companies.length === 0) {
    return (
      <div className="text-center py-12">
        <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">
          暂无企业数据
        </h3>
        <p className="text-gray-500">尝试调整搜索条件或筛选器</p>
      </div>
    )
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {companies.map(company => (
        <Link key={company.id} href={`/companies/${company.id}`}>
          <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <CardTitle className="text-xl">{company.name}</CardTitle>
                    {company.isVerified && (
                      <Badge variant="secondary" className="text-xs">
                        已验证
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    {company.industry && <span>{company.industry}</span>}
                    {company.industry && company.size && <span>•</span>}
                    {company.size && (
                      <span>{companySizeMap[company.size]}</span>
                    )}
                    {(company.industry || company.size) &&
                      company.headquarters && <span>•</span>}
                    {company.headquarters && (
                      <>
                        <MapPin className="h-3 w-3" />
                        <span>{company.headquarters}</span>
                      </>
                    )}
                  </div>
                </div>
                {/* 企业Logo占位 */}
                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  {company.logo ? (
                    <img
                      src={company.logo}
                      alt={company.name}
                      className="w-12 h-12 object-contain"
                    />
                  ) : (
                    <span className="text-xs text-gray-400">Logo</span>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* 描述 */}
              {company.description && (
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {company.description}
                </p>
              )}

              {/* 评分信息 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  {company.averageRating &&
                  typeof company.averageRating === 'number' ? (
                    <div className="flex items-center">
                      <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      <span className="ml-1 font-semibold">
                        {company.averageRating.toFixed(1)}
                      </span>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-500">暂无评分</span>
                  )}
                  {company.totalReviews > 0 && (
                    <span className="text-sm text-gray-600">
                      {company.totalReviews} 条评价
                    </span>
                  )}
                </div>
                {company.totalSalaries > 0 && (
                  <Badge variant="secondary">
                    {company.totalSalaries} 条薪资
                  </Badge>
                )}
              </div>

              {/* 快速统计 */}
              <div className="grid grid-cols-3 gap-2 pt-4 border-t">
                <div className="text-center">
                  <Users className="h-4 w-4 mx-auto mb-1 text-gray-400" />
                  <p className="text-xs text-gray-600">评价</p>
                  <p className="text-sm font-semibold">
                    {company.totalReviews}
                  </p>
                </div>
                <div className="text-center">
                  <TrendingUp className="h-4 w-4 mx-auto mb-1 text-gray-400" />
                  <p className="text-xs text-gray-600">薪资</p>
                  <p className="text-sm font-semibold">
                    {company.totalSalaries}
                  </p>
                </div>
                <div className="text-center">
                  <Star className="h-4 w-4 mx-auto mb-1 text-gray-400" />
                  <p className="text-xs text-gray-600">评分</p>
                  <p className="text-sm font-semibold">
                    {company.averageRating &&
                    typeof company.averageRating === 'number'
                      ? company.averageRating.toFixed(1)
                      : 'N/A'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  )
}

/**
 * 加载骨架屏组件
 */
function CompanyListSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {[1, 2, 3, 4, 5, 6].map(i => (
        <Card key={i} className="animate-pulse">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="grid grid-cols-3 gap-2 pt-4 border-t">
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
