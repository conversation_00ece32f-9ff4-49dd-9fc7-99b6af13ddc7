import { Suspense } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Filter, 
  TrendingUp, 
  DollarSign, 
  Users, 
  MapPin,
  Plus,
  BarChart3,
  Target
} from 'lucide-react'

/**
 * 薪资查询页面
 * 展示和搜索各企业的薪资信息
 */
export default function SalariesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">薪资查询</h1>
        <p className="text-gray-600">查看真实的薪资数据，了解市场行情</p>
      </div>

      {/* 搜索和筛选栏 */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-5">
            {/* 职位搜索 */}
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input placeholder="搜索职位..." className="pl-10" />
              </div>
            </div>

            {/* 公司搜索 */}
            <div>
              <Input placeholder="公司名称" />
            </div>

            {/* 城市筛选 */}
            <div>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择城市" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beijing">北京</SelectItem>
                  <SelectItem value="shanghai">上海</SelectItem>
                  <SelectItem value="guangzhou">广州</SelectItem>
                  <SelectItem value="shenzhen">深圳</SelectItem>
                  <SelectItem value="hangzhou">杭州</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 经验筛选 */}
            <div>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="工作经验" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0-1">0-1年</SelectItem>
                  <SelectItem value="1-3">1-3年</SelectItem>
                  <SelectItem value="3-5">3-5年</SelectItem>
                  <SelectItem value="5-10">5-10年</SelectItem>
                  <SelectItem value="10+">10年以上</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-between items-center mt-4">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              更多筛选
            </Button>
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                找到 <span className="font-semibold">2,456</span> 条薪资记录
              </span>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                贡献薪资
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 内容标签页 */}
      <Tabs defaultValue="list" className="space-y-4">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="list">薪资列表</TabsTrigger>
          <TabsTrigger value="analysis">数据分析</TabsTrigger>
          <TabsTrigger value="trends">趋势报告</TabsTrigger>
        </TabsList>

        {/* 薪资列表 */}
        <TabsContent value="list" className="space-y-4">
          <Suspense fallback={<SalaryListSkeleton />}>
            <SalaryList />
          </Suspense>
        </TabsContent>

        {/* 数据分析 */}
        <TabsContent value="analysis" className="space-y-4">
          <SalaryAnalysis />
        </TabsContent>

        {/* 趋势报告 */}
        <TabsContent value="trends" className="space-y-4">
          <SalaryTrends />
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 薪资列表组件
 */
function SalaryList() {
  // 模拟薪资数据
  const salaries = [
    {
      id: 1,
      position: '前端工程师',
      company: '阿里巴巴',
      location: '杭州',
      experience: '3-5年',
      baseSalary: 280000,
      bonus: 50000,
      stock: 30000,
      totalSalary: 360000,
      level: 'P6',
      submittedAt: '2024-01-15',
      verified: true,
    },
    {
      id: 2,
      position: '后端工程师',
      company: '腾讯',
      location: '深圳',
      experience: '5-10年',
      baseSalary: 320000,
      bonus: 80000,
      stock: 60000,
      totalSalary: 460000,
      level: 'T3-2',
      submittedAt: '2024-01-10',
      verified: true,
    },
    {
      id: 3,
      position: '产品经理',
      company: '字节跳动',
      location: '北京',
      experience: '3-5年',
      baseSalary: 300000,
      bonus: 60000,
      stock: 40000,
      totalSalary: 400000,
      level: '2-1',
      submittedAt: '2024-01-08',
      verified: false,
    },
    {
      id: 4,
      position: '算法工程师',
      company: '美团',
      location: '北京',
      experience: '1-3年',
      baseSalary: 250000,
      bonus: 40000,
      stock: 20000,
      totalSalary: 310000,
      level: 'L6',
      submittedAt: '2024-01-05',
      verified: true,
    },
  ]

  return (
    <div className="space-y-4">
      {salaries.map(salary => (
        <Card key={salary.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                {/* 职位和公司信息 */}
                <div className="flex items-center gap-2 mb-3">
                  <h3 className="text-lg font-semibold">{salary.position}</h3>
                  <Badge variant={salary.verified ? 'default' : 'secondary'}>
                    {salary.verified ? '已验证' : '待验证'}
                  </Badge>
                </div>

                <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                  <span className="font-medium">{salary.company}</span>
                  <span className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {salary.location}
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {salary.experience}
                  </span>
                  <span>职级: {salary.level}</span>
                </div>

                {/* 薪资构成 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">基本工资</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      ¥{salary.baseSalary.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">年终奖</p>
                    <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                      ¥{salary.bonus.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">股票</p>
                    <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                      ¥{salary.stock.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">总包</p>
                    <p className="text-xl font-bold text-blue-600 dark:text-blue-400">
                      ¥{salary.totalSalary.toLocaleString()}
                    </p>
                  </div>
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-400">
                  提交时间: {salary.submittedAt}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex flex-col gap-2 ml-4">
                <Button variant="outline" size="sm">
                  查看详情
                </Button>
                <Button variant="ghost" size="sm">
                  举报
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * 数据分析组件
 */
function SalaryAnalysis() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {/* 平均薪资 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            平均薪资
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600">前端工程师</p>
              <p className="text-2xl font-bold">¥325,000</p>
              <p className="text-sm text-green-600">+12% vs 去年</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">后端工程师</p>
              <p className="text-2xl font-bold">¥358,000</p>
              <p className="text-sm text-green-600">+15% vs 去年</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">产品经理</p>
              <p className="text-2xl font-bold">¥298,000</p>
              <p className="text-sm text-green-600">+8% vs 去年</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 薪资分布 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            薪资分布
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">10-20万</span>
              <div className="flex-1 mx-3 bg-gray-200 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '15%' }}></div>
              </div>
              <span className="text-sm text-gray-600">15%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">20-30万</span>
              <div className="flex-1 mx-3 bg-gray-200 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '35%' }}></div>
              </div>
              <span className="text-sm text-gray-600">35%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">30-50万</span>
              <div className="flex-1 mx-3 bg-gray-200 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '30%' }}></div>
              </div>
              <span className="text-sm text-gray-600">30%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">50万以上</span>
              <div className="flex-1 mx-3 bg-gray-200 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '20%' }}></div>
              </div>
              <span className="text-sm text-gray-600">20%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 热门职位 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            热门职位
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm">Java后端工程师</span>
              <Badge variant="secondary">892条</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">前端工程师</span>
              <Badge variant="secondary">756条</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">产品经理</span>
              <Badge variant="secondary">634条</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">算法工程师</span>
              <Badge variant="secondary">523条</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * 趋势报告组件
 */
function SalaryTrends() {
  return (
    <div className="space-y-6">
      {/* 趋势图表卡片 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            薪资趋势变化
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center border rounded-lg bg-gray-50">
            <p className="text-gray-600">趋势图表将在这里显示</p>
          </div>
        </CardContent>
      </Card>

      {/* 行业分析 */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>行业薪资对比</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>互联网</span>
                <span className="font-semibold">¥358K</span>
              </div>
              <div className="flex justify-between items-center">
                <span>金融</span>
                <span className="font-semibold">¥298K</span>
              </div>
              <div className="flex justify-between items-center">
                <span>制造业</span>
                <span className="font-semibold">¥245K</span>
              </div>
              <div className="flex justify-between items-center">
                <span>教育</span>
                <span className="font-semibold">¥189K</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>城市薪资对比</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>北京</span>
                <span className="font-semibold">¥342K</span>
              </div>
              <div className="flex justify-between items-center">
                <span>上海</span>
                <span className="font-semibold">¥335K</span>
              </div>
              <div className="flex justify-between items-center">
                <span>深圳</span>
                <span className="font-semibold">¥328K</span>
              </div>
              <div className="flex justify-between items-center">
                <span>杭州</span>
                <span className="font-semibold">¥298K</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

/**
 * 薪资列表加载骨架屏
 */
function SalaryListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-3"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="grid grid-cols-4 gap-4">
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 