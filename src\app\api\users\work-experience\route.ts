import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 工作经历验证模式
const createWorkExperienceSchema = z.object({
  companyName: z
    .string()
    .min(1, '公司名称不能为空')
    .max(200, '公司名称最多200个字符'),
  position: z.string().min(1, '职位不能为空').max(100, '职位最多100个字符'),
  department: z.string().max(100, '部门最多100个字符').optional(),
  employmentType: z.enum([
    'FULL_TIME',
    'PART_TIME',
    'CONTRACT',
    'INTERNSHIP',
    'FREELANCE',
  ]),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, '开始日期格式不正确'),
  endDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, '结束日期格式不正确')
    .optional(),
  isCurrent: z.boolean().default(false),
  description: z.string().max(2000, '工作描述最多2000个字符').optional(),
  achievements: z.array(z.string()).default([]),
  skills: z.array(z.string()).default([]),
  salary: z.number().positive('薪资必须是正数').optional(),
  currency: z.string().max(10, '货币代码最多10个字符').default('CNY'),
  isPublic: z.boolean().default(false),
})

/**
 * 获取当前用户的工作经历列表
 * GET /api/users/work-experience
 *
 * 查询参数:
 * - page: 页码 (默认 1)
 * - limit: 每页数量 (默认 10)
 * - status: 审核状态筛选 (PENDING, APPROVED, REJECTED, REVOKED)
 * - company: 公司名称搜索
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再获取工作经历',
          },
        },
        { status: 401 }
      )
    }

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10'), 50) // 限制最大50条
    const status = url.searchParams.get('status')
    const company = url.searchParams.get('company')

    // 构建查询条件
    const where: any = {
      userId: session.user.id,
    }

    if (
      status &&
      ['PENDING', 'APPROVED', 'REJECTED', 'REVOKED'].includes(status)
    ) {
      where.verificationStatus = status
    }

    if (company) {
      where.companyName = {
        contains: company,
        mode: 'insensitive',
      }
    }

    // 执行查询
    const [workExperiences, total] = await Promise.all([
      prisma.workExperience.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: [
          { isCurrent: 'desc' }, // 当前工作排在前面
          { startDate: 'desc' }, // 按开始日期倒序
        ],
        select: {
          id: true,
          companyName: true,
          position: true,
          department: true,
          employmentType: true,
          startDate: true,
          endDate: true,
          isCurrent: true,
          description: true,
          achievements: true,
          skills: true,
          salary: true,
          currency: true,
          verificationStatus: true,
          isPublic: true,
          createdAt: true,
          updatedAt: true,
          verifiedAt: true,
        },
      }),
      prisma.workExperience.count({ where }),
    ])

    // 计算工作年限
    const experiencesWithDuration = workExperiences.map(exp => {
      const start = new Date(exp.startDate)
      const end = exp.endDate ? new Date(exp.endDate) : new Date()
      const durationMonths = Math.floor(
        (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 30)
      )

      return {
        ...exp,
        durationMonths,
        durationText: formatDuration(durationMonths),
      }
    })

    // 计算总统计
    const stats = {
      total,
      verified: workExperiences.filter(
        exp => exp.verificationStatus === 'APPROVED'
      ).length,
      pending: workExperiences.filter(
        exp => exp.verificationStatus === 'PENDING'
      ).length,
      totalMonths: experiencesWithDuration.reduce(
        (sum, exp) => sum + exp.durationMonths,
        0
      ),
    }

    return NextResponse.json({
      success: true,
      message: '获取工作经历成功',
      data: experiencesWithDuration,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats,
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取工作经历失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取工作经历失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 创建新的工作经历
 * POST /api/users/work-experience
 *
 * 需要用户登录
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再添加工作经历',
          },
        },
        { status: 401 }
      )
    }

    const body = await request.json()

    // 验证输入数据
    const validatedData = createWorkExperienceSchema.parse(body)

    // 日期验证
    const startDate = new Date(validatedData.startDate)
    const endDate = validatedData.endDate
      ? new Date(validatedData.endDate)
      : null

    if (endDate && startDate >= endDate) {
      return NextResponse.json(
        {
          success: false,
          message: '日期验证失败',
          error: {
            code: 'INVALID_DATE_RANGE',
            message: '开始日期必须早于结束日期',
          },
        },
        { status: 400 }
      )
    }

    // 如果标记为当前工作，需要将其他工作的 isCurrent 设为 false
    if (validatedData.isCurrent) {
      await prisma.workExperience.updateMany({
        where: {
          userId: session.user.id,
          isCurrent: true,
        },
        data: {
          isCurrent: false,
        },
      })
    }

    // 创建工作经历
    const workExperience = await prisma.workExperience.create({
      data: {
        ...validatedData,
        userId: session.user.id,
        startDate: startDate,
        endDate: endDate,
        verificationStatus: 'PENDING', // 新创建的工作经历需要审核
      },
      select: {
        id: true,
        companyName: true,
        position: true,
        department: true,
        employmentType: true,
        startDate: true,
        endDate: true,
        isCurrent: true,
        verificationStatus: true,
        createdAt: true,
      },
    })

    // 更新用户积分（添加工作经历奖励）
    await updateUserPoints(session.user.id, 'WORK_EXPERIENCE_ADDED', 15)

    return NextResponse.json(
      {
        success: true,
        message: '工作经历添加成功',
        data: workExperience,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('创建工作经历失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '创建工作经历失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 格式化工作时长
 */
function formatDuration(months: number): string {
  if (months < 1) return '不足1个月'

  const years = Math.floor(months / 12)
  const remainingMonths = months % 12

  let result = ''
  if (years > 0) {
    result += `${years}年`
  }
  if (remainingMonths > 0) {
    result += `${remainingMonths}个月`
  }

  return result || '不足1个月'
}

/**
 * 更新用户积分
 */
async function updateUserPoints(
  userId: string,
  action: string,
  points: number
) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        points: { increment: points },
        reputation: { increment: points * 0.1 },
      },
    })
  } catch (error) {
    console.error('更新用户积分失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
