-- 用户表: 存储用户的基本信息
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- 用户唯一标识符 (UUID)，默认生成
    username VARCHAR(50) UNIQUE NOT NULL, -- 用户名，唯一且不能为空
    email VARCHAR(100) UNIQUE NOT NULL, -- 电子邮箱，唯一且不能为空
    password_hash VARCHAR(255) NOT NULL, -- 加密后的用户密码哈希值
    profile_picture_url TEXT, -- 用户头像图片的URL
    github_id VARCHAR(255) UNIQUE, -- GitHub第三方登录的唯一ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- 记录最后更新时间
);

-- 公司表: 存储公司的基本信息
CREATE TABLE companies (
    id BIGSERIAL PRIMARY KEY, -- 公司唯一标识符，自增整数
    name VARCHAR(255) NOT NULL, -- 公司名称，不能为空
    description TEXT, -- 公司简介
    website_url TEXT, -- 公司官网URL
    logo_url TEXT, -- 公司Logo图片的URL
    industry VARCHAR(100), -- 所属行业
    headquarters_location VARCHAR(255), -- 总部所在地
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- 记录最后更新时间
);

-- 薪资表: 存储用户提交的薪资信息
CREATE TABLE salaries (
    id BIGSERIAL PRIMARY KEY, -- 薪资记录唯一标识符，自增整数
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- 关联的用户ID，如果用户被删除，此字段设为NULL
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE, -- 关联的公司ID，如果公司被删除，相关薪资记录也一并删除
    job_title VARCHAR(255) NOT NULL, -- 职位名称，不能为空
    salary DECIMAL(12, 2) NOT NULL, -- 薪资数额，不能为空
    currency VARCHAR(10) NOT NULL DEFAULT 'USD', -- 货币单位，默认为美元
    location VARCHAR(255), -- 工作地点
    work_experience_level VARCHAR(50), -- 工作经验水平 (如：初级, 中级, 高级)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    is_anonymous BOOLEAN DEFAULT TRUE -- 是否匿名提交，默认为匿名
);

-- 面试经验表: 存储用户提交的面试经验分享
CREATE TABLE interviews (
    id BIGSERIAL PRIMARY KEY, -- 面试经验记录唯一标识符，自增整数
    user_id UUID REFERENCES users(id) ON DELETE SET NULL, -- 关联的用户ID，如果用户被删除，此字段设为NULL
    company_id BIGINT REFERENCES companies(id) ON DELETE CASCADE, -- 关联的公司ID，如果公司被删除，相关面试记录也一并删除
    job_title VARCHAR(255) NOT NULL, -- 面试职位，不能为空
    content TEXT NOT NULL, -- 面试经验的具体内容，不能为空
    difficulty_level SMALLINT, -- 面试难度评级 (如 1-5)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    is_anonymous BOOLEAN DEFAULT TRUE -- 是否匿名提交，默认为匿名
);

-- 论坛帖子表
CREATE TABLE posts (
    id BIGSERIAL PRIMARY KEY, -- 帖子唯一标识符，自增整数
    user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- 作者的用户ID，如果用户被删除，其所有帖子也一并删除
    title VARCHAR(255) NOT NULL, -- 帖子标题，不能为空
    content TEXT NOT NULL, -- 帖子内容，不能为空
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- 记录最后更新时间
);

-- 论坛评论表
CREATE TABLE comments (
    id BIGSERIAL PRIMARY KEY, -- 评论唯一标识符，自增整数
    post_id BIGINT REFERENCES posts(id) ON DELETE CASCADE, -- 所属帖子的ID，如果帖子被删除，相关评论也一并删除
    user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- 评论者的用户ID，如果用户被删除，其所有评论也一并删除
    content TEXT NOT NULL, -- 评论内容，不能为空
    parent_comment_id BIGINT REFERENCES comments(id) ON DELETE CASCADE, -- 父评论ID，用于实现嵌套评论。如果父评论被删除，子评论也一并删除
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- 记录创建时间
);

-- 用户收藏表: 记录用户收藏的帖子
CREATE TABLE user_favorites (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE, -- 用户ID
    post_id BIGINT REFERENCES posts(id) ON DELETE CASCADE, -- 帖子ID
    PRIMARY KEY (user_id, post_id) -- 使用用户ID和帖子ID作为联合主键，确保每个用户对一个帖子的收藏是唯一的
);

-- 通知表: 存储发送给用户的通知信息
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY, -- 通知唯一标识符，自增整数
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 接收通知的用户ID
    type VARCHAR(50) NOT NULL, -- 通知类型 (例如: 'new_comment' - 新评论, 'post_mention' - 帖子中被提及)
    related_entity_id BIGINT, -- 关联实体的ID (例如: 帖子ID, 评论ID)
    related_entity_type VARCHAR(50), -- 关联实体的类型 (例如: 'post', 'comment')
    is_read BOOLEAN DEFAULT FALSE, -- 通知是否已读，默认为未读
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP -- 记录创建时间
);
