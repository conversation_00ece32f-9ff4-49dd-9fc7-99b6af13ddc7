# WorkMates 项目需求文档

## 项目概述

### 项目背景
WorkMates 是一个专为职场人士打造的信息分享与交流社区，类似于"看准网"，旨在为求职者和在职人员提供真实、透明的企业信息和职场交流平台。

### 项目目标
- 构建可信的企业信息数据库
- 提供安全的匿名信息分享环境
- 打造活跃的职场交流社区
- 帮助用户做出明智的职业决策

### 技术选型
- **前端框架**: Next.js 15 (App Router)
- **开发语言**: TypeScript
- **数据库**: Supabase PostgreSQL
- **ORM**: Prisma
- **认证**: NextAuth.js v5
- **UI框架**: Tailwind CSS + shadcn/ui
- **部署平台**: Vercel

## 功能需求

### 1. 用户认证系统

#### 1.1 用户注册
- 邮箱密码注册
- Google OAuth 快速注册
- 用户信息验证
- 邮箱验证机制

#### 1.2 用户登录
- 邮箱密码登录
- Google OAuth 登录
- 记住登录状态
- 自动登录功能

#### 1.3 用户管理
- 个人资料编辑
- 密码修改
- 账户安全设置
- 隐私设置控制

### 2. 企业信息系统

#### 2.1 企业信息管理
- 企业基本信息展示
- 企业详情页面
- 企业图片和介绍
- 企业联系方式

#### 2.2 企业搜索功能
- 企业名称搜索
- 行业分类筛选
- 企业规模筛选
- 地区位置筛选

#### 2.3 企业评价系统
- 多维度评分（薪资、环境、管理、发展）
- 企业评价展示
- 评分统计分析
- 评价真实性验证

### 3. 薪资数据系统

#### 3.1 薪资信息收集
- 匿名薪资分享
- 职位薪资录入
- 薪资构成详情
- 工作年限关联

#### 3.2 薪资数据分析
- 薪资统计图表
- 行业薪资对比
- 薪资趋势分析
- 薪资分位数展示

#### 3.3 薪资查询功能
- 按企业查询薪资
- 按职位查询薪资
- 按工作年限筛选
- 按地区筛选薪资

### 4. 面试经验系统

#### 4.1 面试经验分享
- 面试流程记录
- 面试题目分享
- 面试官评价
- 面试结果反馈

#### 4.2 面试经验查询
- 按企业查询面经
- 按职位查询面经
- 面试难度筛选
- 面试时间筛选

#### 4.3 面试数据统计
- 面试通过率统计
- 面试难度分析
- 面试流程分析
- 面试题目分类

### 5. 职场论坛系统

#### 5.1 帖子管理
- 帖子发布功能
- 帖子分类管理
- 帖子标签系统
- 帖子编辑删除

#### 5.2 互动功能
- 评论回复系统
- 点赞收藏功能
- 帖子分享功能
- 用户关注功能

#### 5.3 内容管理
- 帖子审核机制
- 违规内容举报
- 内容质量评估
- 热门内容推荐

### 6. 用户中心系统

#### 6.1 个人资料管理
- 基本信息编辑
- 头像上传功能
- 职业信息管理
- 联系方式设置

#### 6.2 工作经历管理
- 工作经历录入
- 工作证明上传
- 经历真实性验证
- 经历隐私设置

#### 6.3 消息系统
- 系统消息通知
- 私信功能
- 消息已读状态
- 消息分类管理

### 7. 搜索系统

#### 7.1 全局搜索
- 关键词搜索
- 搜索结果分类
- 搜索历史记录
- 热门搜索推荐

#### 7.2 高级搜索
- 多条件组合搜索
- 搜索结果排序
- 搜索结果筛选
- 搜索结果导出

### 8. 管理系统

#### 8.1 内容审核
- 帖子内容审核
- 评论内容审核
- 举报处理流程
- 违规内容处理

#### 8.2 用户管理
- 用户权限管理
- 用户行为监控
- 用户封禁功能
- 用户等级系统

## 非功能需求

### 1. 性能要求
- 页面加载时间 < 3秒
- 数据库查询响应时间 < 500ms
- 支持并发用户数 > 1000
- 系统可用性 > 99.5%

### 2. 安全要求
- 用户数据加密存储
- API接口安全验证
- 防止SQL注入攻击
- 防止XSS攻击

### 3. 兼容性要求
- 支持主流浏览器
- 响应式设计适配移动端
- 支持多种屏幕分辨率
- 良好的可访问性

### 4. 可维护性要求
- 代码结构清晰
- 完善的文档说明
- 单元测试覆盖率 > 80%
- 错误日志记录完整

## 项目约束

### 1. 技术约束
- 必须使用 Next.js 15 框架
- 必须使用 TypeScript 开发
- 必须使用 Supabase 作为数据库
- 必须部署在 Vercel 平台

### 2. 时间约束
- 项目开发周期为 3 个月
- 每个功能模块开发时间不超过 2 周
- 测试阶段时间为 2 周
- 部署上线时间为 1 周

### 3. 资源约束
- 开发团队规模为 1-2 人
- 服务器资源有限
- 第三方服务成本控制
- 存储空间限制

## 验收标准

### 1. 功能验收
- 所有核心功能正常运行
- 用户流程完整可用
- 数据准确性验证
- 界面友好易用

### 2. 性能验收
- 满足性能要求指标
- 压力测试通过
- 兼容性测试通过
- 安全性测试通过

### 3. 质量验收
- 代码质量检查通过
- 文档完整性检查
- 测试覆盖率达标
- 用户体验评估合格
