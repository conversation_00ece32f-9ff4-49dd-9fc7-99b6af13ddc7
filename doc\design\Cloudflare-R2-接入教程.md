# Cloudflare R2 对象存储接入教程

## 📋 目录
1. [什么是 Cloudflare R2](#什么是-cloudflare-r2)
2. [为什么选择 R2](#为什么选择-r2)
3. [准备工作](#准备工作)
4. [创建 R2 存储桶](#创建-r2-存储桶)
5. [获取 API 凭证](#获取-api-凭证)
6. [项目集成](#项目集成)
7. [实现文件上传](#实现文件上传)
8. [实现文件访问](#实现文件访问)
9. [安全配置](#安全配置)
10. [测试验证](#测试验证)

## 什么是 Cloudflare R2

Cloudflare R2 是 Cloudflare 提供的对象存储服务，类似于 Amazon S3，但具有以下优势：
- **零出站费用**：从 R2 下载数据不收费
- **S3 兼容**：可以使用现有的 S3 工具和 SDK
- **全球分布**：利用 Cloudflare 的全球网络
- **高性能**：低延迟访问
- **成本效益**：存储费用比 S3 更低

## 为什么选择 R2

对于 WorkMates 项目，R2 特别适合：
1. **用户头像存储**：频繁访问，零出站费用节省成本
2. **工作证明文件**：安全存储用户上传的文档
3. **企业 Logo**：快速加载企业相关图片
4. **全球用户**：利用 CDN 加速访问

## 准备工作

### 1. 注册 Cloudflare 账户
1. 访问 [Cloudflare 官网](https://www.cloudflare.com/)
2. 点击 "Sign Up" 注册账户
3. 验证邮箱并完成注册

### 2. 启用 R2 服务
1. 登录 Cloudflare Dashboard
2. 在左侧菜单找到 "R2 Object Storage"
3. 点击 "Purchase R2" 或 "Enable R2"
4. 选择付费计划（有免费额度）

## 创建 R2 存储桶

### 1. 创建存储桶
```bash
# 在 Cloudflare Dashboard 中
1. 进入 R2 Object Storage
2. 点击 "Create bucket"
3. 输入存储桶名称：workmates-assets
4. 选择区域（建议选择离用户最近的区域）
5. 点击 "Create bucket"
```

### 2. 存储桶结构规划
```
workmates-assets/
├── avatars/           # 用户头像
│   ├── {userId}-{timestamp}.jpg
│   └── {userId}-{timestamp}.png
├── company-logos/     # 企业 Logo
│   ├── {companyId}-logo.png
│   └── {companyId}-banner.jpg
├── work-files/        # 工作证明文件
│   ├── {userId}/
│   │   ├── resume.pdf
│   │   └── certificate.jpg
└── documents/         # 其他文档
    └── {userId}/
        └── {documentId}.pdf
```

## 获取 API 凭证

### 1. 创建 API Token
```bash
# 在 Cloudflare Dashboard 中
1. 进入 "My Profile" > "API Tokens"
2. 点击 "Create Token"
3. 选择 "Custom token"
4. 配置权限：
   - Account: Cloudflare R2:Edit
   - Zone Resources: Include All zones
5. 点击 "Continue to summary"
6. 点击 "Create Token"
7. 复制并保存 Token（只显示一次）
```

### 2. 获取账户 ID
```bash
# 在 Cloudflare Dashboard 中
1. 进入右侧边栏
2. 找到 "Account ID"
3. 复制账户 ID
```

### 3. 获取 R2 访问密钥
```bash
# 在 R2 Dashboard 中
1. 进入 "R2 Object Storage"
2. 点击 "Manage R2 API tokens"
3. 点击 "Create API token"
4. 选择权限：
   - Object Read & Write
   - 选择特定存储桶或所有存储桶
5. 点击 "Create API token"
6. 保存 Access Key ID 和 Secret Access Key
```

## 项目集成

### 1. 安装依赖
```bash
npm install @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
npm install @types/multer multer
```

### 2. 环境变量配置
```bash
# .env.local
# Cloudflare R2 配置
R2_ACCOUNT_ID=e42adfd74794cbebc7d2fc635f1a5edd
R2_ACCESS_KEY_ID=94515e7d58b302a0e387827e47fd7e1e
R2_SECRET_ACCESS_KEY=78a0c92e1388d44796132d1cde5d8f31a257a6f371fd5e93090d18840c505bb5
R2_BUCKET_NAME=workmates
R2_PUBLIC_URL=https://e42adfd74794cbebc7d2fc635f1a5edd.r2.cloudflarestorage.com/workmates

# 可选：自定义域名
# R2_CUSTOM_DOMAIN=https://assets.workmates.com
```

### 3. 创建 R2 客户端
```typescript
// src/lib/r2-client.ts
import { S3Client } from '@aws-sdk/client-s3'

const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  },
})

export default r2Client
```

## 实现文件上传

### 1. 创建上传工具函数
```typescript
// src/lib/r2-upload.ts
import { PutObjectCommand } from '@aws-sdk/client-s3'
import r2Client from './r2-client'
import crypto from 'crypto'

export interface UploadOptions {
  file: Buffer
  fileName: string
  contentType: string
  folder: 'avatars' | 'company-logos' | 'work-files' | 'documents'
  userId?: string
}

export async function uploadToR2({
  file,
  fileName,
  contentType,
  folder,
  userId
}: UploadOptions) {
  // 生成唯一文件名
  const timestamp = Date.now()
  const hash = crypto.randomBytes(8).toString('hex')
  const extension = fileName.split('.').pop()
  const uniqueFileName = userId 
    ? `${userId}-${timestamp}-${hash}.${extension}`
    : `${timestamp}-${hash}.${extension}`
  
  const key = `${folder}/${uniqueFileName}`
  
  const command = new PutObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME!,
    Key: key,
    Body: file,
    ContentType: contentType,
    Metadata: {
      originalName: fileName,
      uploadedBy: userId || 'anonymous',
      uploadedAt: new Date().toISOString(),
    },
  })

  try {
    await r2Client.send(command)
    
    // 返回公共访问 URL
    const publicUrl = process.env.R2_CUSTOM_DOMAIN 
      ? `${process.env.R2_CUSTOM_DOMAIN}/${key}`
      : `${process.env.R2_PUBLIC_URL}/${key}`
    
    return {
      success: true,
      url: publicUrl,
      key,
      fileName: uniqueFileName,
    }
  } catch (error) {
    console.error('R2 上传失败:', error)
    throw new Error('文件上传失败')
  }
}
```

### 2. 创建头像上传 API
```typescript
// src/app/api/upload/avatar/route.ts
import { auth } from '@/lib/auth'
import { uploadToR2 } from '@/lib/r2-upload'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '需要登录' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { success: false, message: '请选择文件' },
        { status: 400 }
      )
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, message: '不支持的文件类型' },
        { status: 400 }
      )
    }

    // 验证文件大小（5MB）
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, message: '文件大小不能超过 5MB' },
        { status: 400 }
      )
    }

    // 转换为 Buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // 上传到 R2
    const uploadResult = await uploadToR2({
      file: buffer,
      fileName: file.name,
      contentType: file.type,
      folder: 'avatars',
      userId: session.user.id,
    })

    // 更新数据库
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: { avatar: uploadResult.url },
      select: { id: true, name: true, email: true, avatar: true },
    })

    return NextResponse.json({
      success: true,
      message: '头像上传成功',
      data: {
        user: updatedUser,
        upload: uploadResult,
      },
    })
  } catch (error) {
    console.error('头像上传失败:', error)
    return NextResponse.json(
      { success: false, message: '上传失败' },
      { status: 500 }
    )
  }
}
```

## 实现文件访问

### 1. 公共访问配置
```typescript
// src/lib/r2-access.ts
import { GetObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import r2Client from './r2-client'

// 生成预签名 URL（用于私有文件）
export async function generatePresignedUrl(
  key: string,
  expiresIn: number = 3600 // 1小时
) {
  const command = new GetObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME!,
    Key: key,
  })

  try {
    const url = await getSignedUrl(r2Client, command, { expiresIn })
    return { success: true, url }
  } catch (error) {
    console.error('生成预签名 URL 失败:', error)
    return { success: false, error: '生成访问链接失败' }
  }
}

// 获取公共文件 URL
export function getPublicUrl(key: string) {
  const baseUrl = process.env.R2_CUSTOM_DOMAIN || process.env.R2_PUBLIC_URL
  return `${baseUrl}/${key}`
}
```

### 2. 文件删除功能
```typescript
// src/lib/r2-delete.ts
import { DeleteObjectCommand } from '@aws-sdk/client-s3'
import r2Client from './r2-client'

export async function deleteFromR2(key: string) {
  const command = new DeleteObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME!,
    Key: key,
  })

  try {
    await r2Client.send(command)
    return { success: true }
  } catch (error) {
    console.error('R2 删除失败:', error)
    return { success: false, error: '文件删除失败' }
  }
}
```

## 安全配置

### 1. CORS 配置
```json
// 在 R2 Dashboard 中配置 CORS
{
  "CORSRules": [
    {
      "AllowedOrigins": [
        "http://localhost:3000",
        "https://your-domain.com"
      ],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
      "AllowedHeaders": ["*"],
      "MaxAgeSeconds": 3000
    }
  ]
}
```

### 2. 访问权限配置
```typescript
// src/middleware/upload-auth.ts
import { auth } from '@/lib/auth'
import { NextRequest } from 'next/server'

export async function validateUploadAuth(request: NextRequest) {
  const session = await auth()

  if (!session?.user?.id) {
    throw new Error('需要登录')
  }

  return session
}

// 文件大小限制
export const FILE_SIZE_LIMITS = {
  avatar: 5 * 1024 * 1024,      // 5MB
  document: 20 * 1024 * 1024,   // 20MB
  workFile: 10 * 1024 * 1024,   // 10MB
}

// 文件类型限制
export const ALLOWED_FILE_TYPES = {
  avatar: ['image/jpeg', 'image/png', 'image/webp'],
  document: ['application/pdf', 'application/msword'],
  workFile: ['image/jpeg', 'image/png', 'application/pdf'],
}
```

## 测试验证

### 1. 单元测试
```typescript
// tests/r2-upload.test.ts
import { uploadToR2 } from '@/lib/r2-upload'

describe('R2 Upload', () => {
  test('should upload file successfully', async () => {
    const mockFile = Buffer.from('test file content')

    const result = await uploadToR2({
      file: mockFile,
      fileName: 'test.jpg',
      contentType: 'image/jpeg',
      folder: 'avatars',
      userId: 'test-user-id',
    })

    expect(result.success).toBe(true)
    expect(result.url).toContain('avatars/')
  })
})
```

### 2. 前端测试组件
```typescript
// components/test/R2UploadTest.tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export function R2UploadTest() {
  const [file, setFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleUpload = async () => {
    if (!file) return

    setUploading(true)
    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch('/api/upload/avatar', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      console.error('上传失败:', error)
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="p-4 border rounded">
      <h3>R2 上传测试</h3>
      <input
        type="file"
        accept="image/*"
        onChange={(e) => setFile(e.target.files?.[0] || null)}
      />
      <Button
        onClick={handleUpload}
        disabled={!file || uploading}
      >
        {uploading ? '上传中...' : '上传'}
      </Button>

      {result && (
        <div className="mt-4">
          <pre>{JSON.stringify(result, null, 2)}</pre>
          {result.success && (
            <img
              src={result.data.upload.url}
              alt="上传的图片"
              className="mt-2 max-w-xs"
            />
          )}
        </div>
      )}
    </div>
  )
}
```

## 数据库模型更新

### 1. 更新 User 模型
```prisma
// prisma/schema.prisma
model User {
  id            String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email         String     @unique @db.VarChar(255)
  // ... 其他字段

  // 头像相关字段
  avatar        String?    @db.VarChar(500)  // R2 URL
  avatarKey     String?    @db.VarChar(200)  // R2 对象键，用于删除

  // 文件存储相关
  storageUsed   BigInt     @default(0)       // 已使用存储空间（字节）
  storageLimit  BigInt     @default(104857600) // 存储限制（100MB）

  createdAt     DateTime   @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime   @updatedAt @db.Timestamptz(6)
}
```

### 2. 创建文件记录模型
```prisma
// 文件上传记录
model FileUpload {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId      String   @db.Uuid
  fileName    String   @db.VarChar(255)
  originalName String  @db.VarChar(255)
  fileSize    BigInt
  contentType String   @db.VarChar(100)
  r2Key       String   @db.VarChar(500)  // R2 对象键
  r2Url       String   @db.VarChar(500)  // 公共访问 URL
  folder      String   @db.VarChar(50)   // avatars, documents, etc.
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime @updatedAt @db.Timestamptz(6)

  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("file_uploads")
}
```

## 部署注意事项

### 1. 环境变量检查
```bash
# 生产环境必须设置的变量
R2_ACCOUNT_ID=
R2_ACCESS_KEY_ID=
R2_SECRET_ACCESS_KEY=
R2_BUCKET_NAME=
R2_PUBLIC_URL=
R2_CUSTOM_DOMAIN=  # 可选，但推荐
```

### 2. 自定义域名配置
1. 在 Cloudflare Dashboard 中添加自定义域名
2. 配置 DNS 记录指向 R2 存储桶
3. 启用 SSL/TLS 加密
4. 更新环境变量中的 `R2_CUSTOM_DOMAIN`

### 3. 监控和日志
```typescript
// src/lib/r2-monitor.ts
export function logR2Operation(
  operation: string,
  key: string,
  success: boolean,
  error?: any
) {
  console.log({
    timestamp: new Date().toISOString(),
    operation,
    key,
    success,
    error: error?.message,
  })
}
```

## 成本优化建议

1. **合理设置文件过期策略**
2. **压缩图片文件**
3. **使用 CDN 缓存**
4. **定期清理无用文件**
5. **监控存储使用量**

## 常见问题解决

### 1. CORS 错误
- 检查 R2 CORS 配置
- 确认域名白名单

### 2. 上传失败
- 检查 API 凭证
- 验证文件大小和类型
- 查看网络连接

### 3. 访问被拒绝
- 检查存储桶权限
- 验证 API Token 权限

这个教程应该能帮助你完整地集成 Cloudflare R2 到 WorkMates 项目中。有任何问题都可以随时询问！
```
