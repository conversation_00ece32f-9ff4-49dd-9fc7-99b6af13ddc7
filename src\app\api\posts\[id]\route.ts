import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 帖子类型枚举
const PostType = z.enum([
  'DISCUSSION',
  'QUESTION',
  'SHARING',
  'NEWS',
  'REVIEW',
  'JOB',
])

// 更新帖子的验证模式
const updatePostSchema = z.object({
  title: z
    .string()
    .min(1, '标题不能为空')
    .max(200, '标题最多200个字符')
    .optional(),
  content: z.string().min(1, '内容不能为空').optional(),
  excerpt: z.string().max(500, '摘要最多500个字符').optional(),
  type: PostType.optional(),
  category: z.string().max(50, '分类最多50个字符').optional(),
  tags: z.array(z.string()).optional(),
  companyId: z.string().uuid().optional(),
  isAnonymous: z.boolean().optional(),
})

/**
 * 获取单个帖子详情
 * GET /api/posts/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 获取帖子详情
    const post = await prisma.post.findFirst({
      where: {
        id,
        isDeleted: false,
        isPublished: true,
      },
      select: {
        id: true,
        title: true,
        content: true,
        excerpt: true,
        type: true,
        category: true,
        tags: true,
        companyId: true,
        authorId: true,
        isAnonymous: true,
        isPinned: true,
        isLocked: true,
        viewCount: true,
        likeCount: true,
        commentCount: true,
        shareCount: true,
        createdAt: true,
        updatedAt: true,
        publishedAt: true,
      },
    })

    if (!post) {
      return NextResponse.json(
        {
          success: false,
          message: '帖子不存在',
          error: {
            code: 'POST_NOT_FOUND',
            message: '指定的帖子不存在或已被删除',
          },
        },
        { status: 404 }
      )
    }

    // 增加浏览数
    await prisma.post.update({
      where: { id },
      data: { viewCount: { increment: 1 } },
    })

    // 获取作者信息（非匿名帖子）
    let author = null
    if (!post.isAnonymous && post.authorId) {
      author = await prisma.user.findUnique({
        where: { id: post.authorId },
        select: {
          id: true,
          name: true,
          avatar: true,
          position: true,
          company: true,
          level: true,
          reputation: true,
          isVerified: true,
        },
      })
    }

    // 获取关联公司信息
    let company = null
    if (post.companyId) {
      company = await prisma.company.findUnique({
        where: { id: post.companyId },
        select: {
          id: true,
          name: true,
          logo: true,
          industry: true,
        },
      })
    }

    // 获取最新评论（前5条）
    const recentComments = await prisma.comment.findMany({
      where: {
        postId: id,
        isDeleted: false,
        parentId: null, // 只获取顶级评论
      },
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        content: true,
        authorId: true,
        isAnonymous: true,
        likeCount: true,
        replyCount: true,
        createdAt: true,
      },
    })

    // 为非匿名评论获取作者信息
    const commentsWithAuthor = await Promise.all(
      recentComments.map(async comment => {
        if (comment.isAnonymous || !comment.authorId) {
          return { ...comment, author: null }
        }

        const commentAuthor = await prisma.user.findUnique({
          where: { id: comment.authorId },
          select: {
            id: true,
            name: true,
            avatar: true,
            position: true,
            level: true,
          },
        })

        return { ...comment, author: commentAuthor }
      })
    )

    return NextResponse.json({
      success: true,
      message: '获取帖子详情成功',
      data: {
        ...post,
        viewCount: (post.viewCount || 0) + 1, // 返回增加后的浏览数
        author,
        company,
        recentComments: commentsWithAuthor,
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取帖子详情失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取帖子详情失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新帖子
 * PATCH /api/posts/[id]
 *
 * 需要用户登录且为帖子作者
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再编辑帖子',
          },
        },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    // 验证输入数据
    const validatedData = updatePostSchema.parse(body)

    // 检查帖子是否存在且用户有权限编辑
    const existingPost = await prisma.post.findFirst({
      where: {
        id,
        isDeleted: false,
      },
      select: {
        id: true,
        authorId: true,
        isLocked: true,
        title: true,
        content: true,
      },
    })

    if (!existingPost) {
      return NextResponse.json(
        {
          success: false,
          message: '帖子不存在',
          error: {
            code: 'POST_NOT_FOUND',
            message: '指定的帖子不存在或已被删除',
          },
        },
        { status: 404 }
      )
    }

    // 检查权限：只有作者才能编辑
    if (existingPost.authorId !== session.user.id) {
      return NextResponse.json(
        {
          success: false,
          message: '权限不足',
          error: {
            code: 'FORBIDDEN',
            message: '您只能编辑自己发布的帖子',
          },
        },
        { status: 403 }
      )
    }

    // 检查是否被锁定
    if (existingPost.isLocked) {
      return NextResponse.json(
        {
          success: false,
          message: '帖子已被锁定',
          error: {
            code: 'POST_LOCKED',
            message: '该帖子已被管理员锁定，无法编辑',
          },
        },
        { status: 403 }
      )
    }

    // 如果更新了内容，自动更新摘要
    const updateData: any = { ...validatedData }
    if (validatedData.content && !validatedData.excerpt) {
      updateData.excerpt =
        validatedData.content.substring(0, 200) +
        (validatedData.content.length > 200 ? '...' : '')
    }

    // 更新帖子
    const updatedPost = await prisma.post.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        title: true,
        excerpt: true,
        type: true,
        category: true,
        tags: true,
        isAnonymous: true,
        updatedAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      message: '更新帖子成功',
      data: updatedPost,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('更新帖子失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新帖子失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 删除帖子（软删除）
 * DELETE /api/posts/[id]
 *
 * 需要用户登录且为帖子作者
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再删除帖子',
          },
        },
        { status: 401 }
      )
    }

    const { id } = await params

    // 检查帖子是否存在且用户有权限删除
    const existingPost = await prisma.post.findFirst({
      where: {
        id,
        isDeleted: false,
      },
      select: {
        id: true,
        authorId: true,
        title: true,
      },
    })

    if (!existingPost) {
      return NextResponse.json(
        {
          success: false,
          message: '帖子不存在',
          error: {
            code: 'POST_NOT_FOUND',
            message: '指定的帖子不存在或已被删除',
          },
        },
        { status: 404 }
      )
    }

    // 检查权限：只有作者才能删除
    if (existingPost.authorId !== session.user.id) {
      return NextResponse.json(
        {
          success: false,
          message: '权限不足',
          error: {
            code: 'FORBIDDEN',
            message: '您只能删除自己发布的帖子',
          },
        },
        { status: 403 }
      )
    }

    // 软删除帖子
    await prisma.post.update({
      where: { id },
      data: {
        isDeleted: true,
        isPublished: false,
      },
    })

    return NextResponse.json({
      success: true,
      message: '删除帖子成功',
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('删除帖子失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '删除帖子失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
