/**
 * 通知服务
 * 提供通知相关的API调用接口
 */

import { NotificationType, NotificationPriority, NotificationStatus } from '@prisma/client'

export interface NotificationItem {
  id: string
  userId: string
  type: NotificationType
  title: string
  content?: string
  status: NotificationStatus
  priority: NotificationPriority
  relatedId?: string
  relatedType?: string
  senderId?: string
  senderName?: string
  senderAvatar?: string
  actionUrl?: string
  actionText?: string
  metadata?: any
  readAt?: string
  archivedAt?: string
  expiresAt?: string
  createdAt: string
  updatedAt: string
  sender?: {
    id: string
    name?: string
    username?: string
    avatar?: string
  }
}

export interface NotificationSettings {
  id: string
  userId: string
  // 社交通知设置
  enableLike: boolean
  enableComment: boolean
  enableReply: boolean
  enableFollow: boolean
  enableMention: boolean
  // 工作相关通知设置
  enableJobInvitation: boolean
  enableInterviewInvite: boolean
  enableSalaryRequest: boolean
  enableCompanyUpdate: boolean
  // 内容通知设置
  enablePostFeatured: boolean
  enablePostApproved: boolean
  enablePostRejected: boolean
  enableContentReported: boolean
  // 系统通知设置
  enableSystemUpdate: boolean
  enableSecurityAlert: boolean
  enableAccountVerified: boolean
  enablePolicyUpdate: boolean
  enableMaintenance: boolean
  // 私信通知设置
  enablePrivateMessage: boolean
  enableGroupMessage: boolean
  // 通知渠道设置
  enableWebNotification: boolean
  enableEmailNotification: boolean
  enablePushNotification: boolean
  // 通知时间设置
  quietHoursStart?: string
  quietHoursEnd?: string
  enableQuietHours: boolean
  createdAt: string
  updatedAt: string
}

export interface NotificationStats {
  overview: {
    total: number
    unread: number
    read: number
    archived: number
    readRate: number
  }
  timeStats: {
    today: number
    week: number
  }
  typeStats: Array<{
    type: string
    displayName: string
    count: number
  }>
  priorityStats: Array<{
    priority: string
    displayName: string
    count: number
  }>
  recentNotifications: NotificationItem[]
}

/**
 * 获取通知列表
 */
export async function getNotifications(params: {
  page?: number
  limit?: number
  status?: NotificationStatus
  type?: NotificationType
  priority?: NotificationPriority
} = {}) {
  const searchParams = new URLSearchParams()
  
  if (params.page) searchParams.set('page', params.page.toString())
  if (params.limit) searchParams.set('limit', params.limit.toString())
  if (params.status) searchParams.set('status', params.status)
  if (params.type) searchParams.set('type', params.type)
  if (params.priority) searchParams.set('priority', params.priority)

  const response = await fetch(`/api/notifications?${searchParams}`)
  
  if (!response.ok) {
    throw new Error('获取通知列表失败')
  }

  return await response.json()
}

/**
 * 获取单个通知详情
 */
export async function getNotification(id: string) {
  const response = await fetch(`/api/notifications/${id}`)
  
  if (!response.ok) {
    throw new Error('获取通知详情失败')
  }

  return await response.json()
}

/**
 * 标记通知为已读
 */
export async function markNotificationAsRead(id: string) {
  const response = await fetch(`/api/notifications/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status: 'READ' }),
  })
  
  if (!response.ok) {
    throw new Error('标记通知失败')
  }

  return await response.json()
}

/**
 * 归档通知
 */
export async function archiveNotification(id: string) {
  const response = await fetch(`/api/notifications/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status: 'ARCHIVED' }),
  })
  
  if (!response.ok) {
    throw new Error('归档通知失败')
  }

  return await response.json()
}

/**
 * 删除通知
 */
export async function deleteNotification(id: string) {
  const response = await fetch(`/api/notifications/${id}`, {
    method: 'DELETE',
  })
  
  if (!response.ok) {
    throw new Error('删除通知失败')
  }

  return await response.json()
}

/**
 * 批量标记通知为已读
 */
export async function markNotificationsAsRead(notificationIds?: string[]) {
  const response = await fetch('/api/notifications', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      action: notificationIds ? 'markRead' : 'markAllRead',
      notificationIds,
    }),
  })
  
  if (!response.ok) {
    throw new Error('批量标记通知失败')
  }

  return await response.json()
}

/**
 * 创建通知
 */
export async function createNotification(data: {
  userId: string
  type: NotificationType
  title: string
  content?: string
  priority?: NotificationPriority
  relatedId?: string
  relatedType?: string
  actionUrl?: string
  actionText?: string
  metadata?: any
  expiresAt?: string
}) {
  const response = await fetch('/api/notifications', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  
  if (!response.ok) {
    throw new Error('创建通知失败')
  }

  return await response.json()
}

/**
 * 获取通知设置
 */
export async function getNotificationSettings(): Promise<{ success: boolean; data: NotificationSettings }> {
  const response = await fetch('/api/notifications/settings')
  
  if (!response.ok) {
    throw new Error('获取通知设置失败')
  }

  return await response.json()
}

/**
 * 更新通知设置
 */
export async function updateNotificationSettings(settings: Partial<NotificationSettings>) {
  const response = await fetch('/api/notifications/settings', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(settings),
  })
  
  if (!response.ok) {
    throw new Error('更新通知设置失败')
  }

  return await response.json()
}

/**
 * 重置通知设置
 */
export async function resetNotificationSettings() {
  const response = await fetch('/api/notifications/settings/reset', {
    method: 'POST',
  })
  
  if (!response.ok) {
    throw new Error('重置通知设置失败')
  }

  return await response.json()
}

/**
 * 获取通知统计
 */
export async function getNotificationStats(): Promise<{ success: boolean; data: NotificationStats }> {
  const response = await fetch('/api/notifications/stats')
  
  if (!response.ok) {
    throw new Error('获取通知统计失败')
  }

  return await response.json()
}

/**
 * 获取通知类型的显示文本
 */
export function getNotificationTypeText(type: NotificationType): string {
  const typeMap: Record<NotificationType, string> = {
    LIKE: '点赞',
    COMMENT: '评论',
    REPLY: '回复',
    FOLLOW: '关注',
    MENTION: '提及',
    JOB_INVITATION: '工作邀请',
    INTERVIEW_INVITE: '面试邀请',
    SALARY_REQUEST: '薪资询问',
    COMPANY_UPDATE: '企业更新',
    POST_FEATURED: '帖子推荐',
    POST_APPROVED: '帖子审核通过',
    POST_REJECTED: '帖子被拒绝',
    CONTENT_REPORTED: '内容举报',
    SYSTEM_UPDATE: '系统更新',
    SECURITY_ALERT: '安全提醒',
    ACCOUNT_VERIFIED: '账户验证',
    POLICY_UPDATE: '政策更新',
    MAINTENANCE: '系统维护',
    PRIVATE_MESSAGE: '私信',
    GROUP_MESSAGE: '群消息',
  }
  return typeMap[type] || type
}

/**
 * 获取通知优先级的显示文本
 */
export function getNotificationPriorityText(priority: NotificationPriority): string {
  const priorityMap: Record<NotificationPriority, string> = {
    LOW: '低优先级',
    NORMAL: '普通',
    HIGH: '高优先级',
    URGENT: '紧急',
  }
  return priorityMap[priority] || priority
}

/**
 * 获取通知状态的显示文本
 */
export function getNotificationStatusText(status: NotificationStatus): string {
  const statusMap: Record<NotificationStatus, string> = {
    UNREAD: '未读',
    READ: '已读',
    ARCHIVED: '已归档',
  }
  return statusMap[status] || status
}
