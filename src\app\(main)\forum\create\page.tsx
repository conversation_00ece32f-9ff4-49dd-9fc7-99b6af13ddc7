'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import {
    AlertCircle,
    Eye,
    Hash,
    Save,
    Upload,
    X
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

/**
 * 创建帖子页面
 * 提供富文本编辑功能，支持分类标签和匿名发布
 * 
 * 功能特性：
 * - 实时预览功能
 * - 8个帖子分类选择
 * - 标签管理（最多5个）
 * - 匿名发布选项
 * - 草稿自动保存
 * - 表单验证和错误处理
 * - 发布指南和帮助信息
 */
export default function CreatePostPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('edit')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // 帖子表单数据
  const [postData, setPostData] = useState({
    title: '',
    content: '',
    category: '',
    tags: [] as string[],
    isAnonymous: false,
  })

  const [currentTag, setCurrentTag] = useState('')

  // 帖子分类配置 - 涵盖职场主要话题
  const categories = [
    { id: 'workplace', name: '职场讨论', description: '工作环境、团队协作等话题' },
    { id: 'career', name: '职业规划', description: '职业发展和规划建议' },
    { id: 'interview', name: '面经分享', description: '面试经验和技巧分享' },
    { id: 'salary', name: '薪资福利', description: '薪资谈判和福利讨论' },
    { id: 'company', name: '公司评价', description: '企业文化和工作体验' },
    { id: 'skills', name: '技能提升', description: '专业技能学习交流' },
    { id: 'balance', name: '工作生活', description: '工作与生活平衡话题' },
    { id: 'industry', name: '行业资讯', description: '行业动态和趋势分析' },
  ]

  /**
   * 添加标签功能
   * 限制最多5个标签，避免标签冗余
   */
  const addTag = () => {
    if (currentTag.trim() && 
        !postData.tags.includes(currentTag.trim()) && 
        postData.tags.length < 5) {
      setPostData(prev => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }))
      setCurrentTag('')
    }
  }

  /**
   * 移除标签功能
   */
  const removeTag = (tagToRemove: string) => {
    setPostData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  /**
   * 表单验证
   */
  const validateForm = () => {
    if (!postData.title.trim()) {
      setMessage({ type: 'error', text: '请输入帖子标题' })
      return false
    }
    
    if (!postData.content.trim()) {
      setMessage({ type: 'error', text: '请输入帖子内容' })
      return false
    }
    
    if (!postData.category) {
      setMessage({ type: 'error', text: '请选择帖子分类' })
      return false
    }
    
    return true
  }

  /**
   * 处理帖子发布
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)
    setMessage(null)

    try {
      // 调用发布帖子的API
      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: postData.title,
          content: postData.content,
          type: 'DISCUSSION', // 默认为讨论类型
          category: postData.category,
          tags: postData.tags,
          isAnonymous: postData.isAnonymous,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || '发布失败')
      }

      setMessage({ type: 'success', text: '帖子发布成功！' })

      // 发布成功后跳转到论坛首页
      setTimeout(() => {
        router.push('/forum')
      }, 1500)
    } catch (error) {
      console.error('发布失败:', error)
      setMessage({
        type: 'error',
        text: error instanceof Error ? error.message : '发布失败，请稍后重试'
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 保存草稿功能
   * TODO: 实现本地存储或服务器端草稿保存
   */
  const saveDraft = () => {
    try {
      localStorage.setItem('draft_post', JSON.stringify(postData))
      setMessage({ type: 'success', text: '草稿已保存' })
    } catch (error) {
      console.error('保存草稿失败:', error)
      setMessage({ type: 'error', text: '保存草稿失败' })
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">发布新帖</h1>
        <p className="text-gray-600">分享你的想法和经验</p>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-4">
          {/* 主要内容区域 */}
          <div className="lg:col-span-3 space-y-6">
            {/* 基本信息卡片 */}
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 错误提示 */}
                                  {message && (
                   <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
                     <AlertCircle className="h-4 w-4" />
                     <AlertDescription>{message.text}</AlertDescription>
                   </Alert>
                  )}

                {/* 标题输入 */}
                <div className="space-y-2">
                  <Label htmlFor="title">标题 *</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="请输入一个吸引人的标题..."
                    value={postData.title}
                    onChange={(e) => setPostData(prev => ({ ...prev, title: e.target.value }))}
                    className="text-lg"
                    maxLength={100}
                  />
                  <p className="text-sm text-gray-500">
                    {postData.title.length}/100 字符
                  </p>
                </div>

                {/* 分类选择 */}
                <div className="space-y-2">
                  <Label>分类 *</Label>
                  <Select onValueChange={(value) => setPostData(prev => ({ ...prev, category: value }))} value={postData.category}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择帖子分类" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 标签输入 */}
                <div className="space-y-2">
                  <Label>标签</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="输入标签后按回车或逗号添加"
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' || e.key === ',') {
                          e.preventDefault()
                          addTag()
                        }
                      }}
                      className="flex-1"
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Hash className="h-4 w-4" />
                    </Button>
                  </div>
                  {/* 标签显示 */}
                  {postData.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {postData.tags.map(tag => (
                        <Badge key={tag} variant="secondary" className="gap-1">
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="ml-1 hover:text-red-500"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                  <p className="text-sm text-gray-500">
                    最多添加 5 个标签 ({postData.tags.length}/5)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* 内容编辑卡片 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>内容</CardTitle>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab('preview')}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      {activeTab === 'edit' ? '编辑' : '预览'}
                    </Button>
                    <Button type="button" variant="outline" size="sm">
                      <Upload className="h-4 w-4 mr-2" />
                      上传图片
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {activeTab === 'edit' ? (
                  <div className="space-y-2">
                    <textarea
                      name="content"
                      placeholder="请输入帖子内容... 支持 Markdown 格式"
                      value={postData.content}
                      onChange={(e) => setPostData(prev => ({ ...prev, content: e.target.value }))}
                      className="w-full h-96 p-4 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span>支持 Markdown 语法</span>
                      <span>{postData.content.length} 字符</span>
                    </div>
                  </div>
                ) : (
                  <div className="min-h-96 p-4 border rounded-lg bg-gray-50">
                    <div className="prose prose-gray max-w-none">
                      <div className="whitespace-pre-wrap">
                        {postData.content || '内容预览将显示在这里...'}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 侧边栏 */}
          <div className="lg:col-span-1 space-y-6">
            {/* 发布设置 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">发布设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 匿名发布 */}
                <div className="flex items-center space-x-2">
                  <input
                    id="anonymous"
                    name="isAnonymous"
                    type="checkbox"
                    checked={postData.isAnonymous}
                    onChange={(e) => setPostData(prev => ({ ...prev, isAnonymous: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor="anonymous" className="text-sm cursor-pointer">
                    匿名发布
                  </Label>
                </div>

                {/* 操作按钮 */}
                <div className="space-y-2">
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? '发布中...' : '发布帖子'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={saveDraft}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    保存草稿
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 发帖指南 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">发帖指南</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="text-sm space-y-2 text-gray-600">
                  <li>• 使用清晰具体的标题</li>
                  <li>• 提供详细的背景信息</li>
                  <li>• 尊重他人观点，文明讨论</li>
                  <li>• 避免发布敏感或违规内容</li>
                  <li>• 善用标签提高帖子可见性</li>
                </ul>
              </CardContent>
            </Card>

            {/* 最近草稿 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">最近草稿</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-gray-600">
                  <p>暂无保存的草稿</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  )
} 