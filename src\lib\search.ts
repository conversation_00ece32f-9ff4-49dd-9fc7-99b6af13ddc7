/**
 * 搜索服务
 * 提供统一的搜索API调用接口
 */

import { Briefcase, Building, MessageCircle, Star } from 'lucide-react'

export interface SearchFilters {
  types?: string[]
  category?: string
  industry?: string
  sort?: 'relevance' | 'latest' | 'popular' | 'hot'
  limit?: number
  page?: number
}

export interface SearchResultItem {
  id: string
  type: 'company' | 'post' | 'user'
  title: string
  description: string
  url: string
  relevanceScore?: number
  displayName?: string
  subtitle?: string
  authorDisplay?: string
  timeAgo?: string
  rating?: number
  viewCount?: number
  commentCount?: number
  likeCount?: number
  tags?: string[]
  location?: string
  industry?: string
  avatar?: string
  verified?: boolean
  createdAt: string
}

export interface SearchResponse {
  success: boolean
  message: string
  data: {
    companies: SearchResultItem[]
    posts: SearchResultItem[]
    users: SearchResultItem[]
    mixed: SearchResultItem[]
  }
  meta: {
    query: {
      text: string
      types?: string[]
      category?: string
      industry?: string
      sort: string
    }
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
    totals: {
      companies: number
      posts: number
      users: number
      total: number
    }
    suggestions?: string[]
    relatedSearches?: string[]
    timestamp: string
    version: string
  }
}

/**
 * 全局搜索
 */
export async function searchGlobal(
  query: string,
  filters: SearchFilters = {}
): Promise<SearchResponse> {
  try {
    const params = new URLSearchParams({
      q: query,
      sort: filters.sort || 'relevance',
      limit: (filters.limit || 10).toString(),
      ...(filters.types && { types: filters.types.join(',') }),
      ...(filters.category && { category: filters.category }),
      ...(filters.industry && { industry: filters.industry }),
    })

    const response = await fetch(`/api/search?${params}`)
    
    if (!response.ok) {
      throw new Error(`搜索请求失败: ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.message || '搜索失败')
    }

    return data
  } catch (error) {
    console.error('搜索失败:', error)
    throw error
  }
}

/**
 * 企业搜索
 */
export async function searchCompanies(
  query: string,
  filters: {
    industry?: string
    size?: string
    location?: string
    verified?: boolean
    minRating?: number
    hasReviews?: boolean
    hasSalaries?: boolean
    sort?: string
    page?: number
    limit?: number
  } = {}
) {
  try {
    const params = new URLSearchParams({
      q: query,
      page: (filters.page || 1).toString(),
      limit: (filters.limit || 20).toString(),
      sort: filters.sort || 'relevance',
      ...(filters.industry && { industry: filters.industry }),
      ...(filters.size && { size: filters.size }),
      ...(filters.location && { location: filters.location }),
      ...(filters.verified !== undefined && { verified: filters.verified.toString() }),
      ...(filters.minRating && { minRating: filters.minRating.toString() }),
      ...(filters.hasReviews !== undefined && { hasReviews: filters.hasReviews.toString() }),
      ...(filters.hasSalaries !== undefined && { hasSalaries: filters.hasSalaries.toString() }),
    })

    const response = await fetch(`/api/search/companies?${params}`)
    
    if (!response.ok) {
      throw new Error(`企业搜索失败: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('企业搜索失败:', error)
    throw error
  }
}

/**
 * 帖子搜索
 */
export async function searchPosts(
  query: string,
  filters: {
    type?: string
    category?: string
    tags?: string[]
    companyId?: string
    authorId?: string
    anonymous?: boolean
    hasComments?: boolean
    minLikes?: number
    dateFrom?: string
    dateTo?: string
    sort?: string
    page?: number
    limit?: number
  } = {}
) {
  try {
    const params = new URLSearchParams({
      q: query,
      page: (filters.page || 1).toString(),
      limit: (filters.limit || 20).toString(),
      sort: filters.sort || 'relevance',
      ...(filters.type && { type: filters.type }),
      ...(filters.category && { category: filters.category }),
      ...(filters.tags && { tags: filters.tags.join(',') }),
      ...(filters.companyId && { companyId: filters.companyId }),
      ...(filters.authorId && { authorId: filters.authorId }),
      ...(filters.anonymous !== undefined && { anonymous: filters.anonymous.toString() }),
      ...(filters.hasComments !== undefined && { hasComments: filters.hasComments.toString() }),
      ...(filters.minLikes && { minLikes: filters.minLikes.toString() }),
      ...(filters.dateFrom && { dateFrom: filters.dateFrom }),
      ...(filters.dateTo && { dateTo: filters.dateTo }),
    })

    const response = await fetch(`/api/search/posts?${params}`)
    
    if (!response.ok) {
      throw new Error(`帖子搜索失败: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('帖子搜索失败:', error)
    throw error
  }
}

/**
 * 用户搜索
 */
export async function searchUsers(
  query: string,
  filters: {
    sort?: string
    page?: number
    limit?: number
  } = {}
) {
  try {
    const params = new URLSearchParams({
      q: query,
      page: (filters.page || 1).toString(),
      limit: (filters.limit || 20).toString(),
      sort: filters.sort || 'relevance',
    })

    const response = await fetch(`/api/search/users?${params}`)
    
    if (!response.ok) {
      throw new Error(`用户搜索失败: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('用户搜索失败:', error)
    throw error
  }
}

/**
 * 获取搜索建议
 */
export async function getSearchSuggestions(query: string): Promise<string[]> {
  try {
    if (!query.trim()) return []
    
    // 这里可以调用专门的建议API，目前先返回空数组
    // 实际项目中可以基于历史搜索、热门搜索等生成建议
    return []
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    return []
  }
}

/**
 * 格式化搜索结果用于显示
 */
export function formatSearchResults(results: SearchResultItem[]): SearchResultItem[] {
  return results.map(result => ({
    ...result,
    // 确保所有必要字段都有默认值
    description: result.description || '',
    tags: result.tags || [],
    viewCount: result.viewCount || 0,
    commentCount: result.commentCount || 0,
    likeCount: result.likeCount || 0,
  }))
}

/**
 * 获取结果类型的显示文本
 */
export function getResultTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    company: '企业',
    post: '帖子',
    user: '用户',
    job: '职位',
    interview: '面经',
    salary: '薪资',
  }
  return typeMap[type] || type
}

/**
 * 获取结果类型的图标
 */
export function getResultTypeIcon(type: string) {
  const iconMap: Record<string, any> = {
    company: Building,
    post: MessageCircle,
    user: Briefcase,
    job: Briefcase,
    interview: MessageCircle,
    salary: Star,
  }
  return iconMap[type] || MessageCircle
}
