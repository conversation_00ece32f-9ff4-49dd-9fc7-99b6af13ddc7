import { prisma } from './prisma'

/**
 * 数据库连接工具函数
 */

/**
 * 检查数据库连接状态
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`
    return true
  } catch (error) {
    console.error('数据库连接检查失败:', error)
    return false
  }
}

/**
 * 带重试机制的数据库操作
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      console.warn(`数据库操作失败 (尝试 ${attempt}/${maxRetries}):`, error)

      if (attempt === maxRetries) {
        break
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }

  throw lastError!
}

/**
 * 安全的数据库操作包装器
 */
export async function safeDbOperation<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T | null> {
  try {
    return await withRetry(operation)
  } catch (error) {
    console.error('数据库操作最终失败:', error)
    return fallback ?? null
  }
}

/**
 * 获取用户资料的安全包装器
 */
export async function getUserProfileSafely(userId: string) {
  return safeDbOperation(async () => {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        username: true,
        phone: true,
        bio: true,
        avatar: true,
        avatarKey: true,
        company: true,
        position: true,
        industry: true,
        education: true,
        experience: true,
        skills: true,
        level: true,
        points: true,
        reputation: true,
        isVerified: true,

        // 新增字段
        storageUsed: true,
        storageLimit: true,
        profileCompleteness: true,
        lastProfileUpdate: true,
        followersCount: true,
        followingCount: true,
        twoFactorEnabled: true,

        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            posts: true,
            comments: true,
            ratings: true,
            salaries: true,
            interviews: true,
            workExperiences: true,
          },
        },
      },
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    return {
      ...user,
      // 转换BigInt字段为字符串以避免JSON序列化错误
      storageUsed: user.storageUsed ? user.storageUsed.toString() : '0',
      storageLimit: user.storageLimit ? user.storageLimit.toString() : '104857600',
      stats: {
        postsCount: user._count.posts,
        commentsCount: user._count.comments,
        ratingsCount: user._count.ratings,
        salariesCount: user._count.salaries,
        interviewsCount: user._count.interviews,
        workExperiencesCount: user._count.workExperiences,
        totalContributions:
          user._count.posts +
          user._count.comments +
          user._count.ratings +
          user._count.salaries +
          user._count.interviews,
      },
    }
  })
}
