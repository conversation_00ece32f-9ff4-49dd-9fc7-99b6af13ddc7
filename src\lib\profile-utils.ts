/**
 * 用户资料相关工具函数
 */

interface UserProfileData {
  name?: string | null
  bio?: string | null
  avatar?: string | null
  company?: string | null
  position?: string | null
  industry?: string | null
  education?: string | null
  experience?: number | null
  skills?: string[]
  phone?: string | null
}

/**
 * 计算用户资料完整度
 * @param profile 用户资料数据
 * @returns 完整度百分比 (0-100)
 */
export function calculateProfileCompleteness(profile: UserProfileData): number {
  const fields = [
    { key: 'name', weight: 15 },
    { key: 'bio', weight: 10 },
    { key: 'avatar', weight: 15 },
    { key: 'company', weight: 10 },
    { key: 'position', weight: 10 },
    { key: 'industry', weight: 8 },
    { key: 'education', weight: 8 },
    { key: 'experience', weight: 8 },
    { key: 'skills', weight: 10 },
    { key: 'phone', weight: 6 },
  ]

  let totalScore = 0
  let maxScore = 0

  fields.forEach(field => {
    maxScore += field.weight
    const value = profile[field.key as keyof UserProfileData]
    
    if (field.key === 'skills') {
      // 技能数组，至少有一个技能才算完整
      if (Array.isArray(value) && value.length > 0) {
        totalScore += field.weight
      }
    } else if (field.key === 'experience') {
      // 工作年限，大于等于0才算有效
      if (typeof value === 'number' && value >= 0) {
        totalScore += field.weight
      }
    } else {
      // 其他字段，非空即可
      if (value && value.toString().trim().length > 0) {
        totalScore += field.weight
      }
    }
  })

  return Math.round((totalScore / maxScore) * 100)
}

/**
 * 获取资料完整度建议
 * @param profile 用户资料数据
 * @returns 建议列表
 */
export function getProfileCompletionSuggestions(profile: UserProfileData): string[] {
  const suggestions: string[] = []

  if (!profile.name || profile.name.trim().length === 0) {
    suggestions.push('添加真实姓名')
  }

  if (!profile.avatar) {
    suggestions.push('上传头像照片')
  }

  if (!profile.bio || profile.bio.trim().length === 0) {
    suggestions.push('填写个人简介')
  }

  if (!profile.company || profile.company.trim().length === 0) {
    suggestions.push('添加当前公司信息')
  }

  if (!profile.position || profile.position.trim().length === 0) {
    suggestions.push('填写职位信息')
  }

  if (!profile.industry || profile.industry.trim().length === 0) {
    suggestions.push('选择所在行业')
  }

  if (!profile.education || profile.education.trim().length === 0) {
    suggestions.push('添加教育背景')
  }

  if (typeof profile.experience !== 'number' || profile.experience < 0) {
    suggestions.push('填写工作年限')
  }

  if (!Array.isArray(profile.skills) || profile.skills.length === 0) {
    suggestions.push('添加专业技能')
  }

  if (!profile.phone || profile.phone.trim().length === 0) {
    suggestions.push('添加联系电话')
  }

  return suggestions
}

/**
 * 格式化存储空间大小
 * @param bytes 字节数
 * @returns 格式化的字符串
 */
export function formatStorageSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 计算存储空间使用百分比
 * @param used 已使用空间（字节）
 * @param limit 总限制空间（字节）
 * @returns 使用百分比 (0-100)
 */
export function calculateStorageUsagePercentage(used: number, limit: number): number {
  if (limit === 0) return 0
  return Math.round((used / limit) * 100)
}

/**
 * 检查是否可以上传文件
 * @param currentUsed 当前已使用空间（字节）
 * @param fileSize 要上传的文件大小（字节）
 * @param limit 存储限制（字节）
 * @returns 是否可以上传
 */
export function canUploadFile(currentUsed: number, fileSize: number, limit: number): boolean {
  return (currentUsed + fileSize) <= limit
}

/**
 * 获取用户等级信息
 * @param level 用户等级
 * @returns 等级信息
 */
export function getUserLevelInfo(level: string) {
  const levelMap = {
    NEWBIE: { name: '新手', color: 'gray', description: '刚刚加入的新用户' },
    JUNIOR: { name: '初级', color: 'green', description: '有一定贡献的用户' },
    INTERMEDIATE: { name: '中级', color: 'blue', description: '活跃的社区成员' },
    SENIOR: { name: '高级', color: 'purple', description: '经验丰富的贡献者' },
    EXPERT: { name: '专家', color: 'orange', description: '领域专家' },
    MASTER: { name: '大师', color: 'red', description: '社区意见领袖' },
  }

  return levelMap[level as keyof typeof levelMap] || levelMap.NEWBIE
}

/**
 * 验证用户名格式
 * @param username 用户名
 * @returns 验证结果
 */
export function validateUsername(username: string): { valid: boolean; message?: string } {
  if (!username || username.trim().length === 0) {
    return { valid: false, message: '用户名不能为空' }
  }

  if (username.length < 3) {
    return { valid: false, message: '用户名至少需要3个字符' }
  }

  if (username.length > 20) {
    return { valid: false, message: '用户名不能超过20个字符' }
  }

  if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    return { valid: false, message: '用户名只能包含字母、数字、下划线和连字符' }
  }

  return { valid: true }
}

/**
 * 验证手机号格式
 * @param phone 手机号
 * @returns 验证结果
 */
export function validatePhone(phone: string): { valid: boolean; message?: string } {
  if (!phone || phone.trim().length === 0) {
    return { valid: true } // 手机号是可选的
  }

  // 简单的手机号验证（支持中国大陆手机号）
  if (!/^1[3-9]\d{9}$/.test(phone)) {
    return { valid: false, message: '请输入有效的手机号码' }
  }

  return { valid: true }
}

/**
 * 生成用户显示名称
 * @param name 真实姓名
 * @param username 用户名
 * @param email 邮箱
 * @returns 显示名称
 */
export function generateDisplayName(name?: string | null, username?: string | null, email?: string | null): string {
  if (name && name.trim().length > 0) {
    return name.trim()
  }
  
  if (username && username.trim().length > 0) {
    return username.trim()
  }
  
  if (email && email.trim().length > 0) {
    return email.split('@')[0]
  }
  
  return '未知用户'
}

/**
 * 生成头像后备字符
 * @param name 姓名
 * @param email 邮箱
 * @returns 头像后备字符
 */
export function generateAvatarFallback(name?: string | null, email?: string | null): string {
  if (name && name.trim().length > 0) {
    return name.trim()[0].toUpperCase()
  }
  
  if (email && email.trim().length > 0) {
    return email[0].toUpperCase()
  }
  
  return '?'
}
