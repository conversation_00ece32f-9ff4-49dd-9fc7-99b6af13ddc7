'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  CheckCheck, 
  Settings, 
  Trash2, 
  Archive,
  Bell,
  BellOff,
  ExternalLink
} from 'lucide-react'
import Link from 'next/link'
import { NotificationItem } from './notification-item'
import { 
  getNotifications, 
  markNotificationsAsRead,
  NotificationItem as NotificationData,
  NotificationStatus
} from '@/lib/notifications'
import { useToast } from '@/hooks/use-toast'

interface NotificationCenterProps {
  onNotificationUpdate?: () => void
  onClose?: () => void
}

export function NotificationCenter({ 
  onNotificationUpdate, 
  onClose 
}: NotificationCenterProps) {
  const { toast } = useToast()
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<NotificationStatus | 'all'>('all')
  const [unreadCount, setUnreadCount] = useState(0)

  // 获取通知列表
  const fetchNotifications = async (status?: NotificationStatus) => {
    try {
      setLoading(true)
      const response = await getNotifications({
        status,
        limit: 20,
      })
      
      if (response.success) {
        setNotifications(response.data.notifications)
        setUnreadCount(response.data.unreadCount || 0)
      }
    } catch (error) {
      console.error('获取通知失败:', error)
      toast({
        title: '获取通知失败',
        description: '请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    fetchNotifications(activeTab === 'all' ? undefined : activeTab)
  }, [activeTab])

  // 标记所有通知为已读
  const handleMarkAllAsRead = async () => {
    try {
      await markNotificationsAsRead()
      await fetchNotifications(activeTab === 'all' ? undefined : activeTab)
      onNotificationUpdate?.()
      
      toast({
        title: '操作成功',
        description: '所有通知已标记为已读',
      })
    } catch (error) {
      console.error('标记通知失败:', error)
      toast({
        title: '操作失败',
        description: '请稍后重试',
        variant: 'destructive',
      })
    }
  }

  // 处理通知项更新
  const handleNotificationItemUpdate = () => {
    fetchNotifications(activeTab === 'all' ? undefined : activeTab)
    onNotificationUpdate?.()
  }

  return (
    <div className="w-full">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          <h3 className="font-semibold">通知中心</h3>
          {unreadCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {unreadCount} 条未读
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              className="text-xs"
            >
              <CheckCheck className="h-3 w-3 mr-1" />
              全部已读
            </Button>
          )}
          
          <Link href="/notifications/settings">
            <Button variant="ghost" size="sm" onClick={onClose}>
              <Settings className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>

      {/* 标签页 */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-4 p-1 m-2">
          <TabsTrigger value="all" className="text-xs">
            全部
          </TabsTrigger>
          <TabsTrigger value="UNREAD" className="text-xs">
            未读
          </TabsTrigger>
          <TabsTrigger value="READ" className="text-xs">
            已读
          </TabsTrigger>
          <TabsTrigger value="ARCHIVED" className="text-xs">
            归档
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-0">
          <ScrollArea className="h-96">
            {loading ? (
              // 加载状态
              <div className="p-4 space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="flex items-start gap-3 p-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4" />
                        <div className="h-3 bg-gray-200 rounded w-1/2" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : notifications.length > 0 ? (
              // 通知列表
              <div className="divide-y">
                {notifications.map((notification) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onUpdate={handleNotificationItemUpdate}
                  />
                ))}
              </div>
            ) : (
              // 空状态
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <BellOff className="h-12 w-12 text-muted-foreground mb-4" />
                <h4 className="font-medium mb-2">暂无通知</h4>
                <p className="text-sm text-muted-foreground">
                  {activeTab === 'UNREAD' 
                    ? '所有通知都已阅读' 
                    : activeTab === 'ARCHIVED'
                    ? '暂无归档通知'
                    : '暂时没有新通知'}
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>

      {/* 底部操作 */}
      <Separator />
      <div className="p-3 flex justify-between items-center">
        <Link 
          href="/notifications" 
          className="text-sm text-primary hover:underline flex items-center gap-1"
          onClick={onClose}
        >
          查看全部通知
          <ExternalLink className="h-3 w-3" />
        </Link>
        
        <div className="text-xs text-muted-foreground">
          {notifications.length > 0 && `显示最近 ${notifications.length} 条`}
        </div>
      </div>
    </div>
  )
}
