'use client'

import { NotificationBell } from '@/components/notifications/notification-bell'
import { Button } from '@/components/ui/button'
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import {
    Building2,
    ChevronDown,
    DollarSign,
    Menu,
    MessageSquare,
    Search,
    Users
} from 'lucide-react'
import { signOut, useSession } from 'next-auth/react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'

/**
 * 头部导航组件
 *
 * 功能特性：
 * - PC端hover触发下拉菜单，左边缘对齐
 * - 移动端点击展开/收起菜单，与PC端行为一致
 * - 搜索功能弹窗
 * - 三大功能区域：企业查询、薪资面经、社区论坛
 */
export function Header() {
  const pathname = usePathname()
  const router = useRouter()
  const { data: session, status } = useSession()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [searchOpen, setSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedMobileItems, setExpandedMobileItems] = useState<string[]>([])
  const [userDropdownOpen, setUserDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false)
      }
    }

    if (userDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [userDropdownOpen])

  // 导航菜单配置
  const navigation = [
    {
      name: '企业查询',
      href: '/companies',
      icon: Building2,
      description: '搜索企业信息、查看企业评价和详细资料',
      children: [
        {
          name: '企业列表',
          href: '/companies',
          description: '浏览所有企业信息，支持筛选和搜索',
        },
        {
          name: '企业详情',
          href: '/companies/1',
          description: '查看特定企业的详细资料和评价',
        },
      ],
    },
    {
      name: '薪资面经',
      href: '/companies/salaries',
      icon: DollarSign,
      description: '查看真实薪资数据和面试经验分享',
      children: [
        {
          name: '薪资数据',
          href: '/companies/salaries',
          description: '查看各公司真实薪资水平和统计',
        },
        {
          name: '面试经验',
          href: '/companies/interviews',
          description: '分享和查看面试流程与技巧',
        },
      ],
    },
    {
      name: '社区论坛',
      href: '/forum',
      icon: MessageSquare,
      description: '职场交流与经验分享社区',
      children: [
        {
          name: '热门讨论',
          href: '/forum',
          description: '查看社区最热门的职场话题',
        },
        {
          name: '发布内容',
          href: '/forum/create',
          description: '分享你的职场见解和经验',
        },
      ],
    },
  ]

  // 搜索功能处理
  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
      setSearchOpen(false)
      setSearchQuery('')
    }
  }

  // 搜索键盘事件处理
  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  // 移动端菜单交互处理
  const handleMobileMenuClick = (item: (typeof navigation)[0]) => {
    if (item.children) {
      // 切换展开状态
      setExpandedMobileItems(prev =>
        prev.includes(item.name)
          ? prev.filter(name => name !== item.name)
          : [...prev, item.name]
      )
    } else {
      // 直接跳转
      router.push(item.href)
      setMobileMenuOpen(false)
    }
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/80 shadow-sm">
      <nav className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center gap-8">
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg group-hover:bg-blue-700 transition-colors">
                {/* 用户图标 */}
                <Users className="w-4 h-4 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">WorkMates</span>
            </Link>

            {/* 桌面端导航 */}
            <div className="hidden lg:flex space-x-2">
              {navigation.map(item => (
                <div key={item.name} className="relative group">
                  {item.children ? (
                    <>
                      <button
                        className={cn(
                          'flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors',
                          pathname.startsWith(item.href) &&
                            'text-blue-600 bg-blue-50'
                        )}
                      >
                        <item.icon className="mr-2 h-4 w-4" />
                        {item.name}
                        <ChevronDown className="ml-1 h-3 w-3" />
                      </button>

                      {/* 下拉菜单 - 左边缘对齐 */}
                      <div className="absolute left-0 top-full mt-1 w-[300px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                        <div className="bg-white rounded-md border shadow-lg p-4">
                          <div className="mb-3">
                            <h4 className="text-sm font-semibold text-gray-900 mb-1">
                              {item.name}
                            </h4>
                            <p className="text-xs text-gray-600">
                              {item.description}
                            </p>
                          </div>

                          <div className="grid gap-2">
                            {item.children.map(child => (
                              <Link
                                key={child.href}
                                href={child.href}
                                className="block select-none rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-blue-50 focus:bg-blue-50 group/item"
                              >
                                <div className="text-sm font-medium text-gray-900 group-hover/item:text-blue-600">
                                  {child.name}
                                </div>
                                <p className="text-xs text-gray-600 mt-1">
                                  {child.description}
                                </p>
                              </Link>
                            ))}
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <Link
                      href={item.href}
                      className={cn(
                        'flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors',
                        pathname.startsWith(item.href) &&
                          'text-blue-600 bg-blue-50'
                      )}
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 右侧操作区域 */}
          <div className="flex items-center gap-3">
            {/* 搜索对话框 */}
            <Dialog open={searchOpen} onOpenChange={setSearchOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="hidden md:flex text-gray-600 hover:text-blue-600"
                >
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>搜索企业或内容</DialogTitle>
                </DialogHeader>
                <div className="flex items-center space-x-2">
                  <div className="grid flex-1 gap-2">
                    <Input
                      placeholder="输入企业名称、职位或关键词..."
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      onKeyPress={handleSearchKeyPress}
                      className="w-full"
                    />
                  </div>
                  <Button onClick={handleSearch} disabled={!searchQuery.trim()}>
                    搜索
                  </Button>
                </div>
              </DialogContent>
            </Dialog>

            {/* 认证按钮 */}
            <div className="hidden md:flex items-center gap-3">
              {session?.user ? (
                // 已登录用户显示用户头像下拉菜单
                <div className="flex items-center gap-3">
                  {/* 通知铃铛 */}
                  <NotificationBell />
                  {/* 用户信息下拉菜单 */}
                  <div className="relative" ref={dropdownRef}>
                    <button
                      className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded-lg transition-colors"
                      onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                    >
                      {session.user.image ? (
                        <img
                          src={session.user.image}
                          alt={session.user.name || ''}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium">
                          {(session.user.name || session.user.email || '?')[0].toUpperCase()}
                        </div>
                      )}
                      <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>

                    {/* 下拉菜单 */}
                    {userDropdownOpen && (
                      <div className="absolute right-0 top-full mt-2 w-56 bg-white rounded-lg border shadow-lg z-50">
                        <div className="py-1">
                          {/* 用户信息头部 */}
                          <div className="px-4 py-3 border-b border-gray-100">
                            <div className="flex items-center gap-3">
                              {session.user.image ? (
                                <img
                                  src={session.user.image}
                                  alt={session.user.name || ''}
                                  className="w-10 h-10 rounded-full object-cover"
                                />
                              ) : (
                                <div className="w-10 h-10 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium">
                                  {(session.user.name || session.user.email || '?')[0].toUpperCase()}
                                </div>
                              )}
                              <div className="flex-1 min-w-0">
                                <div className="text-sm font-medium text-gray-900 truncate">
                                  {session.user.name || session.user.email}
                                </div>
                                <div className="text-xs text-gray-500 truncate">
                                  {session.user.email}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* 个人中心 */}
                          <Link
                            href="/profile"
                            className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setUserDropdownOpen(false)}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            个人资料
                          </Link>

                          {/* 工作经历 */}
                          <Link
                            href="/profile/work-experience"
                            className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setUserDropdownOpen(false)}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6z" />
                            </svg>
                            工作经历
                          </Link>

                          <div className="border-t border-gray-100 my-1"></div>

                          {/* 发布内容 */}
                          <div className="px-2 py-1">
                            <div className="text-xs font-medium text-gray-500 px-2 py-1">发布内容</div>
                            <Link
                              href="/forum/create"
                              className="flex items-center gap-2 px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                              onClick={() => setUserDropdownOpen(false)}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                              </svg>
                              发布帖子
                            </Link>
                            <Link
                              href="/companies/interviews/submit"
                              className="flex items-center gap-2 px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                              onClick={() => setUserDropdownOpen(false)}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              分享面经
                            </Link>
                            <Link
                              href="/companies/salaries/submit"
                              className="flex items-center gap-2 px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                              onClick={() => setUserDropdownOpen(false)}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                              </svg>
                              分享薪资
                            </Link>
                          </div>

                          <div className="border-t border-gray-100 my-1"></div>

                          {/* 账户设置 */}
                          <Link
                            href="/profile/settings"
                            className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setUserDropdownOpen(false)}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            账户设置
                          </Link>

                          <div className="border-t border-gray-100 my-1"></div>

                          {/* 登出 */}
                          <button
                            onClick={async () => {
                              setUserDropdownOpen(false)
                              await signOut({ callbackUrl: '/' })
                            }}
                            className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            登出
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                // 未登录用户显示登录和注册按钮
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    asChild
                    className="text-gray-700 hover:text-blue-600"
                  >
                    <Link href="/auth/login">登录</Link>
                  </Button>
                  <Button
                    size="sm"
                    asChild
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Link href="/auth/register">注册</Link>
                  </Button>
                </>
              )}
            </div>

            {/* 移动端用户头像和菜单按钮 */}
            <div className="lg:hidden flex items-center gap-2">
              {session?.user && (
                <>
                  {/* 移动端通知铃铛 */}
                  <NotificationBell className="lg:hidden" />
                  <div className="relative">
                  <button
                    className="flex items-center p-1 hover:bg-gray-100 rounded-lg transition-colors"
                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                  >
                    {session.user.image ? (
                      <img
                        src={session.user.image}
                        alt={session.user.name || ''}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium">
                        {(session.user.name || session.user.email || '?')[0].toUpperCase()}
                      </div>
                    )}
                  </button>

                  {/* 移动端下拉菜单 */}
                  {userDropdownOpen && (
                    <div className="absolute right-0 top-full mt-2 w-56 bg-white rounded-lg border shadow-lg z-50">
                      <div className="py-1">
                        {/* 用户信息头部 */}
                        <div className="px-4 py-3 border-b border-gray-100">
                          <div className="flex items-center gap-3">
                            {session.user.image ? (
                              <img
                                src={session.user.image}
                                alt={session.user.name || ''}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium">
                                {(session.user.name || session.user.email || '?')[0].toUpperCase()}
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium text-gray-900 truncate">
                                {session.user.name || session.user.email}
                              </div>
                              <div className="text-xs text-gray-500 truncate">
                                {session.user.email}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* 个人中心 */}
                        <Link
                          href="/profile"
                          className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setUserDropdownOpen(false)}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          个人资料
                        </Link>

                        {/* 工作经历 */}
                        <Link
                          href="/profile/work-experience"
                          className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setUserDropdownOpen(false)}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6z" />
                          </svg>
                          工作经历
                        </Link>

                        <div className="border-t border-gray-100 my-1"></div>

                        {/* 发布内容 */}
                        <div className="px-2 py-1">
                          <div className="text-xs font-medium text-gray-500 px-2 py-1">发布内容</div>
                          <Link
                            href="/forum/create"
                            className="flex items-center gap-2 px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                            onClick={() => setUserDropdownOpen(false)}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                            </svg>
                            发布帖子
                          </Link>
                          <Link
                            href="/companies/interviews/submit"
                            className="flex items-center gap-2 px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                            onClick={() => setUserDropdownOpen(false)}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            分享面经
                          </Link>
                          <Link
                            href="/companies/salaries/submit"
                            className="flex items-center gap-2 px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                            onClick={() => setUserDropdownOpen(false)}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                            </svg>
                            分享薪资
                          </Link>
                        </div>

                        <div className="border-t border-gray-100 my-1"></div>

                        {/* 账户设置 */}
                        <Link
                          href="/profile/settings"
                          className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setUserDropdownOpen(false)}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          账户设置
                        </Link>

                        <div className="border-t border-gray-100 my-1"></div>

                        <button
                          onClick={async () => {
                            setUserDropdownOpen(false)
                            await signOut({ callbackUrl: '/' })
                          }}
                          className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                          </svg>
                          登出
                        </button>
                      </div>
                    </div>
                  )}
                </div>
                </>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <Menu className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* 移动端导航菜单 */}
        {mobileMenuOpen && (
          <div className="lg:hidden py-4 border-t bg-white">
            <div className="space-y-2">
              {/* 移动端搜索 */}
              <div className="px-3 pb-3">
                <Dialog open={searchOpen} onOpenChange={setSearchOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-gray-600"
                    >
                      <Search className="h-4 w-4 mr-2" />
                      搜索企业或职位
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>搜索企业或内容</DialogTitle>
                    </DialogHeader>
                    <div className="flex items-center space-x-2">
                      <div className="grid flex-1 gap-2">
                        <Input
                          placeholder="输入企业名称、职位或关键词..."
                          value={searchQuery}
                          onChange={e => setSearchQuery(e.target.value)}
                          onKeyPress={handleSearchKeyPress}
                          className="w-full"
                        />
                      </div>
                      <Button
                        onClick={handleSearch}
                        disabled={!searchQuery.trim()}
                      >
                        搜索
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {/* 移动端导航项目 */}
              {navigation.map(item => {
                const isExpanded = expandedMobileItems.includes(item.name)
                const isActive = pathname.startsWith(item.href)

                return (
                  <div key={item.name} className="space-y-1">
                    {item.children ? (
                      <>
                        <button
                          onClick={() => handleMobileMenuClick(item)}
                          className={cn(
                            'flex items-center justify-between w-full px-3 py-3 text-sm font-medium rounded-lg mx-3 transition-colors',
                            isActive
                              ? 'text-blue-600 bg-blue-50'
                              : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                          )}
                        >
                          <div className="flex items-center">
                            <item.icon className="mr-3 h-5 w-5" />
                            {item.name}
                          </div>
                          <ChevronDown
                            className={cn(
                              'h-4 w-4 transition-transform',
                              isExpanded && 'rotate-180'
                            )}
                          />
                        </button>

                        {/* 展开的子菜单 */}
                        {isExpanded && (
                          <div className="ml-8 space-y-1 pb-2">
                            {item.children.map(child => (
                              <Link
                                key={child.href}
                                href={child.href}
                                className="block px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md mx-3 transition-colors"
                                onClick={() => setMobileMenuOpen(false)}
                              >
                                {child.name}
                              </Link>
                            ))}
                          </div>
                        )}
                      </>
                    ) : (
                      <Link
                        href={item.href}
                        className={cn(
                          'flex items-center px-3 py-3 text-sm font-medium rounded-lg mx-3 transition-colors',
                          isActive
                            ? 'text-blue-600 bg-blue-50'
                            : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                        )}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <item.icon className="mr-3 h-5 w-5" />
                        {item.name}
                      </Link>
                    )}
                  </div>
                )
              })}

              {/* 移动端认证按钮 */}
              {!session?.user && (
                <div className="px-3 pt-3 border-t space-y-2">
                  {/* 未登录用户显示登录和注册按钮 */}
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      asChild
                      className="w-full justify-start"
                    >
                      <Link href="/auth/login">登录</Link>
                    </Button>
                    <Button size="sm" asChild className="w-full">
                      <Link href="/auth/register">注册</Link>
                    </Button>
                  </>
                </div>
              )}
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}
