import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 帖子类型枚举
const PostType = z.enum([
  'DISCUSSION',
  'QUESTION',
  'SHARING',
  'NEWS',
  'REVIEW',
  'JOB',
])

// 创建帖子的验证模式
const createPostSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(200, '标题最多200个字符'),
  content: z.string().min(1, '内容不能为空'),
  excerpt: z.string().max(500, '摘要最多500个字符').optional(),
  type: PostType.default('DISCUSSION'),
  category: z.string().max(50, '分类最多50个字符').optional(),
  tags: z.array(z.string()).default([]),
  companyId: z.string().uuid().optional(),
  isAnonymous: z.boolean().default(false),
})

/**
 * 获取帖子列表
 * GET /api/posts
 *
 * 查询参数:
 * - page: 页码 (默认 1)
 * - limit: 每页数量 (默认 20)
 * - type: 帖子类型筛选
 * - category: 分类筛选
 * - search: 搜索关键词
 * - sort: 排序字段 (latest, popular, hot)
 * - companyId: 关联公司筛选
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const type = url.searchParams.get('type')
    const category = url.searchParams.get('category')
    const search = url.searchParams.get('search')
    const sort = url.searchParams.get('sort') || 'latest'
    const companyId = url.searchParams.get('companyId')

    // 构建查询条件
    const where: any = {
      isDeleted: false,
      isPublished: true,
    }

    if (
      type &&
      ['DISCUSSION', 'QUESTION', 'SHARING', 'NEWS', 'REVIEW', 'JOB'].includes(
        type
      )
    ) {
      where.type = type
    }

    if (category) {
      where.category = category
    }

    if (companyId) {
      where.companyId = companyId
    }

    // 搜索功能
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { excerpt: { contains: search, mode: 'insensitive' } },
      ]
    }

    // 构建排序条件
    let orderBy: any = {}
    switch (sort) {
      case 'popular':
        orderBy = [
          { likeCount: 'desc' },
          { commentCount: 'desc' },
          { createdAt: 'desc' },
        ]
        break
      case 'hot':
        orderBy = [
          { viewCount: 'desc' },
          { likeCount: 'desc' },
          { createdAt: 'desc' },
        ]
        break
      case 'latest':
      default:
        orderBy = { createdAt: 'desc' }
        break
    }

    // 执行查询
    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        select: {
          id: true,
          title: true,
          excerpt: true,
          type: true,
          category: true,
          tags: true,
          companyId: true,
          authorId: true,
          isAnonymous: true,
          isPinned: true,
          viewCount: true,
          likeCount: true,
          commentCount: true,
          shareCount: true,
          createdAt: true,
          publishedAt: true,
        },
        orderBy,
      }),
      prisma.post.count({ where }),
    ])

    // 获取作者信息（非匿名帖子）
    const postsWithAuthor = await Promise.all(
      posts.map(async post => {
        if (post.isAnonymous || !post.authorId) {
          return {
            ...post,
            author: null,
          }
        }

        const author = await prisma.user.findUnique({
          where: { id: post.authorId },
          select: {
            id: true,
            name: true,
            avatar: true,
            position: true,
            level: true,
            reputation: true,
          },
        })

        return {
          ...post,
          author,
        }
      })
    )

    // 获取统计信息
    const stats = await prisma.post.groupBy({
      by: ['type'],
      where: { isDeleted: false, isPublished: true },
      _count: true,
    })

    const typeStats = stats.reduce(
      (acc, stat) => {
        acc[stat.type] = stat._count
        return acc
      },
      {} as Record<string, number>
    )

    return NextResponse.json({
      success: true,
      message: '获取帖子列表成功',
      data: postsWithAuthor,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        statistics: {
          totalPosts: total,
          typeDistribution: typeStats,
        },
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取帖子列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取帖子列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 创建新帖子
 * POST /api/posts
 *
 * 需要用户登录
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再发帖',
          },
        },
        { status: 401 }
      )
    }

    const body = await request.json()

    // 验证输入数据
    const validatedData = createPostSchema.parse(body)

    // 如果指定了公司ID，验证公司是否存在
    if (validatedData.companyId) {
      const company = await prisma.company.findFirst({
        where: { id: validatedData.companyId, isActive: true },
      })

      if (!company) {
        return NextResponse.json(
          {
            success: false,
            message: '指定的公司不存在',
            error: {
              code: 'COMPANY_NOT_FOUND',
              message: '指定的公司不存在或已被删除',
            },
          },
          { status: 404 }
        )
      }
    }

    // 自动生成摘要（如果没有提供）
    const excerpt =
      validatedData.excerpt ||
      validatedData.content.substring(0, 200) +
        (validatedData.content.length > 200 ? '...' : '')

    // 创建帖子
    const post = await prisma.post.create({
      data: {
        ...validatedData,
        excerpt,
        authorId: session.user.id,
        publishedAt: new Date(),
      },
      select: {
        id: true,
        title: true,
        excerpt: true,
        type: true,
        category: true,
        tags: true,
        isAnonymous: true,
        createdAt: true,
        publishedAt: true,
      },
    })

    // 更新用户积分（发帖奖励）
    await updateUserPoints(session.user.id, 'POST_CREATED', 10)

    return NextResponse.json(
      {
        success: true,
        message: '发帖成功',
        data: post,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('创建帖子失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '创建帖子失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新用户积分
 * 给用户增加积分和声誉值
 */
async function updateUserPoints(
  userId: string,
  action: string,
  points: number
) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        points: { increment: points },
        reputation: { increment: points * 0.1 },
      },
    })
  } catch (error) {
    console.error('更新用户积分失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
