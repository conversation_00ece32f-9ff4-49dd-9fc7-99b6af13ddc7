import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 更新工作经历的验证模式
const updateWorkExperienceSchema = z.object({
  companyName: z
    .string()
    .min(1, '公司名称不能为空')
    .max(200, '公司名称最多200个字符')
    .optional(),
  position: z
    .string()
    .min(1, '职位不能为空')
    .max(100, '职位最多100个字符')
    .optional(),
  department: z.string().max(100, '部门最多100个字符').optional(),
  employmentType: z
    .enum(['FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERNSHIP', 'FREELANCE'])
    .optional(),
  startDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, '开始日期格式不正确')
    .optional(),
  endDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, '结束日期格式不正确')
    .optional(),
  isCurrent: z.boolean().optional(),
  description: z.string().max(2000, '工作描述最多2000个字符').optional(),
  achievements: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  salary: z.number().positive('薪资必须是正数').optional(),
  currency: z.string().max(10, '货币代码最多10个字符').optional(),
  isPublic: z.boolean().optional(),
})

/**
 * 获取单个工作经历详情
 * GET /api/users/work-experience/[id]
 *
 * 需要用户登录且为工作经历所有者
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再获取工作经历',
          },
        },
        { status: 401 }
      )
    }

    const { id } = await params

    // 获取工作经历详情
    const workExperience = await prisma.workExperience.findFirst({
      where: {
        id,
        userId: session.user.id, // 确保只能访问自己的工作经历
      },
      select: {
        id: true,
        companyName: true,
        position: true,
        department: true,
        employmentType: true,
        startDate: true,
        endDate: true,
        isCurrent: true,
        description: true,
        achievements: true,
        skills: true,
        salary: true,
        currency: true,
        verificationStatus: true,
        verifiedById: true,
        verifiedAt: true,
        isPublic: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    if (!workExperience) {
      return NextResponse.json(
        {
          success: false,
          message: '工作经历不存在',
          error: {
            code: 'WORK_EXPERIENCE_NOT_FOUND',
            message: '指定的工作经历不存在或无权访问',
          },
        },
        { status: 404 }
      )
    }

    // 计算工作时长
    const start = new Date(workExperience.startDate)
    const end = workExperience.endDate
      ? new Date(workExperience.endDate)
      : new Date()
    const durationMonths = Math.floor(
      (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 30)
    )

    const enrichedWorkExperience = {
      ...workExperience,
      durationMonths,
      durationText: formatDuration(durationMonths),
    }

    return NextResponse.json({
      success: true,
      message: '获取工作经历成功',
      data: enrichedWorkExperience,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取工作经历失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取工作经历失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新工作经历
 * PATCH /api/users/work-experience/[id]
 *
 * 需要用户登录且为工作经历所有者
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再更新工作经历',
          },
        },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    // 验证输入数据
    const validatedData = updateWorkExperienceSchema.parse(body)

    // 检查工作经历是否存在且属于当前用户
    const existingWorkExperience = await prisma.workExperience.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    })

    if (!existingWorkExperience) {
      return NextResponse.json(
        {
          success: false,
          message: '工作经历不存在',
          error: {
            code: 'WORK_EXPERIENCE_NOT_FOUND',
            message: '指定的工作经历不存在或无权访问',
          },
        },
        { status: 404 }
      )
    }

    // 过滤掉undefined的字段
    const updateData = Object.fromEntries(
      Object.entries(validatedData).filter(([, value]) => value !== undefined)
    )

    // 如果没有要更新的数据
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '没有提供要更新的数据',
          error: {
            code: 'NO_UPDATE_DATA',
            message: '请提供至少一个要更新的字段',
          },
        },
        { status: 400 }
      )
    }

    // 创建最终的更新数据对象，包含日期转换
    const finalUpdateData: any = { ...updateData }

    // 日期验证和转换
    if (updateData.startDate || updateData.endDate) {
      const startDate = updateData.startDate
        ? new Date(updateData.startDate as string)
        : existingWorkExperience.startDate
      const endDate = updateData.endDate
        ? new Date(updateData.endDate as string)
        : existingWorkExperience.endDate

      if (endDate && startDate >= endDate) {
        return NextResponse.json(
          {
            success: false,
            message: '日期验证失败',
            error: {
              code: 'INVALID_DATE_RANGE',
              message: '开始日期必须早于结束日期',
            },
          },
          { status: 400 }
        )
      }

      // 转换日期字符串为Date对象
      if (updateData.startDate) {
        finalUpdateData.startDate = new Date(updateData.startDate as string)
      }
      if (updateData.endDate) {
        finalUpdateData.endDate = new Date(updateData.endDate as string)
      }
    }

    // 如果标记为当前工作，需要将其他工作的 isCurrent 设为 false
    if (updateData.isCurrent === true) {
      await prisma.workExperience.updateMany({
        where: {
          userId: session.user.id,
          isCurrent: true,
          id: { not: id }, // 排除当前正在更新的记录
        },
        data: {
          isCurrent: false,
        },
      })
    }

    // 更新工作经历
    const updatedWorkExperience = await prisma.workExperience.update({
      where: { id },
      data: {
        ...finalUpdateData,
        updatedAt: new Date(),
        // 如果修改了重要信息，重新设置为待审核状态
        verificationStatus: shouldResetVerification(finalUpdateData)
          ? 'PENDING'
          : existingWorkExperience.verificationStatus,
      },
      select: {
        id: true,
        companyName: true,
        position: true,
        department: true,
        employmentType: true,
        startDate: true,
        endDate: true,
        isCurrent: true,
        description: true,
        verificationStatus: true,
        updatedAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      message: '工作经历更新成功',
      data: updatedWorkExperience,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('更新工作经历失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新工作经历失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 删除工作经历
 * DELETE /api/users/work-experience/[id]
 *
 * 需要用户登录且为工作经历所有者
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再删除工作经历',
          },
        },
        { status: 401 }
      )
    }

    const { id } = await params

    // 检查工作经历是否存在且属于当前用户
    const existingWorkExperience = await prisma.workExperience.findFirst({
      where: {
        id,
        userId: session.user.id,
      },
    })

    if (!existingWorkExperience) {
      return NextResponse.json(
        {
          success: false,
          message: '工作经历不存在',
          error: {
            code: 'WORK_EXPERIENCE_NOT_FOUND',
            message: '指定的工作经历不存在或无权访问',
          },
        },
        { status: 404 }
      )
    }

    // 删除工作经历（硬删除，因为这是个人数据）
    await prisma.workExperience.delete({
      where: { id },
    })

    return NextResponse.json({
      success: true,
      message: '工作经历删除成功',
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('删除工作经历失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '删除工作经历失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 格式化工作时长
 */
function formatDuration(months: number): string {
  if (months < 1) return '不足1个月'

  const years = Math.floor(months / 12)
  const remainingMonths = months % 12

  let result = ''
  if (years > 0) {
    result += `${years}年`
  }
  if (remainingMonths > 0) {
    result += `${remainingMonths}个月`
  }

  return result || '不足1个月'
}

/**
 * 判断是否需要重新审核
 * 如果修改了关键信息（公司名称、职位、日期），则需要重新审核
 */
function shouldResetVerification(updateData: any): boolean {
  const criticalFields = ['companyName', 'position', 'startDate', 'endDate']
  return criticalFields.some(field => updateData.hasOwnProperty(field))
}
