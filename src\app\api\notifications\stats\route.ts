import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

/**
 * 获取用户通知统计信息
 * GET /api/notifications/stats
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: '未授权访问' },
        { status: 401 }
      )
    }

    const userId = session.user.id

    // 获取各种统计数据
    const [
      totalCount,
      unreadCount,
      readCount,
      archivedCount,
      todayCount,
      weekCount,
      typeStats,
      priorityStats,
      recentNotifications,
    ] = await Promise.all([
      // 总通知数
      prisma.notification.count({
        where: { userId },
      }),
      
      // 未读通知数
      prisma.notification.count({
        where: { userId, status: 'UNREAD' },
      }),
      
      // 已读通知数
      prisma.notification.count({
        where: { userId, status: 'READ' },
      }),
      
      // 已归档通知数
      prisma.notification.count({
        where: { userId, status: 'ARCHIVED' },
      }),
      
      // 今日通知数
      prisma.notification.count({
        where: {
          userId,
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0)),
          },
        },
      }),
      
      // 本周通知数
      prisma.notification.count({
        where: {
          userId,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),
      
      // 按类型统计
      prisma.notification.groupBy({
        by: ['type'],
        where: { userId },
        _count: { type: true },
        orderBy: { _count: { type: 'desc' } },
      }),
      
      // 按优先级统计
      prisma.notification.groupBy({
        by: ['priority'],
        where: { userId },
        _count: { priority: true },
        orderBy: { _count: { priority: 'desc' } },
      }),
      
      // 最近5条通知
      prisma.notification.findMany({
        where: { userId },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              username: true,
              avatar: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
      }),
    ])

    // 计算通知类型的中文名称映射
    const typeDisplayMap: Record<string, string> = {
      LIKE: '点赞',
      COMMENT: '评论',
      REPLY: '回复',
      FOLLOW: '关注',
      MENTION: '提及',
      JOB_INVITATION: '工作邀请',
      INTERVIEW_INVITE: '面试邀请',
      SALARY_REQUEST: '薪资询问',
      COMPANY_UPDATE: '企业更新',
      POST_FEATURED: '帖子推荐',
      POST_APPROVED: '帖子审核通过',
      POST_REJECTED: '帖子被拒绝',
      CONTENT_REPORTED: '内容举报',
      SYSTEM_UPDATE: '系统更新',
      SECURITY_ALERT: '安全提醒',
      ACCOUNT_VERIFIED: '账户验证',
      POLICY_UPDATE: '政策更新',
      MAINTENANCE: '系统维护',
      PRIVATE_MESSAGE: '私信',
      GROUP_MESSAGE: '群消息',
    }

    const priorityDisplayMap: Record<string, string> = {
      LOW: '低优先级',
      NORMAL: '普通',
      HIGH: '高优先级',
      URGENT: '紧急',
    }

    // 格式化类型统计
    const formattedTypeStats = typeStats.map(stat => ({
      type: stat.type,
      displayName: typeDisplayMap[stat.type] || stat.type,
      count: stat._count.type,
    }))

    // 格式化优先级统计
    const formattedPriorityStats = priorityStats.map(stat => ({
      priority: stat.priority,
      displayName: priorityDisplayMap[stat.priority] || stat.priority,
      count: stat._count.priority,
    }))

    // 计算阅读率
    const readRate = totalCount > 0 ? Math.round((readCount / totalCount) * 100) : 0

    return NextResponse.json({
      success: true,
      data: {
        overview: {
          total: totalCount,
          unread: unreadCount,
          read: readCount,
          archived: archivedCount,
          readRate,
        },
        timeStats: {
          today: todayCount,
          week: weekCount,
        },
        typeStats: formattedTypeStats,
        priorityStats: formattedPriorityStats,
        recentNotifications,
      },
    })
  } catch (error) {
    console.error('获取通知统计失败:', error)
    return NextResponse.json(
      { success: false, message: '获取通知统计失败' },
      { status: 500 }
    )
  }
}
