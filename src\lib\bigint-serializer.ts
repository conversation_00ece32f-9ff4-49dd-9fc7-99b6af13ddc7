/**
 * BigInt序列化工具
 * 解决JSON.stringify无法序列化BigInt的问题
 */

/**
 * 递归转换对象中的BigInt字段为字符串
 */
export function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (typeof obj === 'bigint') {
    return obj.toString()
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt)
  }

  if (typeof obj === 'object') {
    const result: any = {}
    for (const [key, value] of Object.entries(obj)) {
      result[key] = serializeBigInt(value)
    }
    return result
  }

  return obj
}

/**
 * 为NextResponse.json提供安全的序列化
 */
export function safeJsonResponse(data: any, init?: ResponseInit) {
  const serializedData = serializeBigInt(data)
  return Response.json(serializedData, init)
}

/**
 * 全局BigInt序列化设置
 * 在应用启动时调用一次
 */
export function setupBigIntSerialization() {
  // 扩展JSON.stringify以支持BigInt
  const originalStringify = JSON.stringify
  JSON.stringify = function(value: any, replacer?: any, space?: any) {
    return originalStringify(value, (key, val) => {
      if (typeof val === 'bigint') {
        return val.toString()
      }
      if (replacer) {
        return replacer(key, val)
      }
      return val
    }, space)
  }
}
