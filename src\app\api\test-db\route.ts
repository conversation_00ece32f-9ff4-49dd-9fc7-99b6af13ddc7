import { PrismaClient } from '@prisma/client'
import { NextResponse } from 'next/server'

const prisma = new PrismaClient()

export async function GET() {
  try {
    // 测试Prisma数据库连接
    const companyCount = await prisma.company.count()
    const companies = await prisma.company.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        description: true,
        averageRating: true,
        totalRatings: true,
        createdAt: true
      }
    })

    // 获取用户总数
    const userCount = await prisma.user.count()

    // 获取帖子总数
    const postCount = await prisma.post.count()

    return NextResponse.json({
      success: true,
      message: '🎉 Supabase + Prisma 数据库连接测试成功！',
      data: {
        数据库状态: '连接正常',
        统计信息: {
          公司总数: companyCount,
          用户总数: userCount,
          帖子总数: postCount
        },
        示例公司数据: companies,
        数据库信息: {
          数据库类型: 'PostgreSQL + Supabase',
          ORM: 'Prisma',
          连接池: '已启用'
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('数据库测试错误:', error)
    return NextResponse.json({
      success: false,
      message: '数据库连接测试失败',
      error: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
} 