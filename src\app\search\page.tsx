'use client'

import { AdvancedSearch, AdvancedSearchFilters } from '@/components/search/advanced-search'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
    getResultTypeIcon,
    getResultTypeText,
    searchGlobal,
    SearchResponse,
    SearchResultItem
} from '@/lib/search'
import { AlertCircle, Building, Clock, Eye, MapPin, MessageCircle, Search, Star, Users } from 'lucide-react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'

export default function SearchPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const query = searchParams?.get('q') || ''

  const [searchQuery, setSearchQuery] = useState(query)
  const [activeTab, setActiveTab] = useState('all')
  const [sortBy, setSortBy] = useState('relevance')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchData, setSearchData] = useState<SearchResponse | null>(null)
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedSearchFilters>({
    query: query,
    type: 'all',
    sort: 'relevance'
  })

  // 执行搜索
  const performSearch = useCallback(async (searchTerm: string, filters?: AdvancedSearchFilters) => {
    if (!searchTerm.trim()) {
      setSearchData(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const searchFilters = filters || advancedFilters
      const response = await searchGlobal(searchTerm, {
        sort: searchFilters.sort as any,
        limit: 20,
        types: searchFilters.type === 'all' ? undefined : [searchFilters.type]
      })
      setSearchData(response)
    } catch (err) {
      console.error('搜索失败:', err)
      setError(err instanceof Error ? err.message : '搜索失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }, [advancedFilters])

  // 初始搜索
  useEffect(() => {
    if (query) {
      performSearch(query)
    }
  }, [query, performSearch])

  // 处理搜索提交
  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  // 处理高级搜索筛选变更
  const handleFiltersChange = (filters: AdvancedSearchFilters) => {
    setAdvancedFilters(filters)
    // 同步基础搜索状态
    setSearchQuery(filters.query)
    setSortBy(filters.sort)
    setActiveTab(filters.type)
  }

  // 应用高级搜索
  const handleAdvancedSearch = () => {
    if (advancedFilters.query.trim()) {
      performSearch(advancedFilters.query, advancedFilters)
      router.push(`/search?q=${encodeURIComponent(advancedFilters.query.trim())}`)
    }
  }

  // 重置高级搜索
  const handleResetFilters = () => {
    const resetFilters: AdvancedSearchFilters = {
      query: searchQuery,
      type: 'all',
      sort: 'relevance'
    }
    setAdvancedFilters(resetFilters)
    setSortBy('relevance')
    setActiveTab('all')
  }

  // 获取当前标签页的结果
  const getCurrentResults = (): SearchResultItem[] => {
    if (!searchData) return []

    switch (activeTab) {
      case 'companies':
        return searchData.data.companies
      case 'posts':
        return searchData.data.posts
      case 'users':
        return searchData.data.users
      default:
        return searchData.data.mixed
    }
  }

  const currentResults = getCurrentResults()

  // 获取结果统计
  const getResultStats = () => {
    if (!searchData) return { total: 0, companies: 0, posts: 0, users: 0 }
    return searchData.meta.totals || { total: 0, companies: 0, posts: 0, users: 0 }
  }

  const stats = getResultStats()

  // 获取结果图标
  const getResultIcon = (type: string) => {
    const IconComponent = getResultTypeIcon(type)
    return <IconComponent className="h-4 w-4" />
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 搜索栏 */}
      <div className="mb-8">
        <div className="flex gap-4 max-w-2xl">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索企业、职位、面经、帖子..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pl-9"
            />
          </div>
          <Button onClick={handleSearch} disabled={loading}>
            {loading ? '搜索中...' : '搜索'}
          </Button>
        </div>
      </div>

      {/* 高级搜索 */}
      <AdvancedSearch
        filters={advancedFilters}
        onFiltersChange={handleFiltersChange}
        onSearch={handleAdvancedSearch}
        onReset={handleResetFilters}
      />

      {/* 错误提示 */}
      {error && (
        <Alert className="mb-6" variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 搜索结果统计 */}
      {searchData && (
        <div className="mb-6">
          <p className="text-muted-foreground">
            搜索 "{searchData.meta.query.text}" 找到 {stats.total} 个结果
          </p>
        </div>
      )}

      {/* 搜索结果区域 */}
      {searchQuery && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* 标签页导航 */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <TabsList className="grid w-full grid-cols-4 lg:w-auto">
              <TabsTrigger value="all" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                全部 ({stats.total})
              </TabsTrigger>
              <TabsTrigger value="companies" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                企业 ({stats.companies})
              </TabsTrigger>
              <TabsTrigger value="posts" className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4" />
                帖子 ({stats.posts})
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                用户 ({stats.users})
              </TabsTrigger>
            </TabsList>

            {/* 排序选择 */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">相关性</SelectItem>
                <SelectItem value="latest">最新发布</SelectItem>
                <SelectItem value="popular">最受欢迎</SelectItem>
                <SelectItem value="hot">最热门</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 搜索结果列表 */}
          <TabsContent value={activeTab} className="mt-6">
            {loading ? (
              // 加载状态
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <Skeleton className="w-12 h-12 rounded-lg" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-20" />
                          <Skeleton className="h-6 w-3/4" />
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-4 w-1/2" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : currentResults.length > 0 ? (
              // 搜索结果
              <div className="space-y-4">
                {currentResults.map((result) => (
                  <Card key={result.id} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                            {getResultIcon(result.type)}
                          </div>
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="secondary" className="text-xs">
                              {getResultTypeText(result.type)}
                            </Badge>
                            {result.rating && (
                              <div className="flex items-center gap-1">
                                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                <span className="text-sm text-muted-foreground">{result.rating}</span>
                              </div>
                            )}
                            {result.verified && (
                              <Badge variant="default" className="text-xs">
                                已认证
                              </Badge>
                            )}
                          </div>

                          <Link href={result.url} className="group">
                            <h3 className="text-lg font-semibold group-hover:text-primary transition-colors mb-2">
                              {result.displayName || result.title}
                            </h3>
                          </Link>

                          <p className="text-muted-foreground mb-3 line-clamp-2">
                            {result.description}
                          </p>

                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            {result.authorDisplay && (
                              <div className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                <span>{result.authorDisplay}</span>
                              </div>
                            )}
                            {result.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                <span>{result.location}</span>
                              </div>
                            )}
                            {result.timeAgo && (
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>{result.timeAgo}</span>
                              </div>
                            )}
                            {result.viewCount !== undefined && result.viewCount > 0 && (
                              <div className="flex items-center gap-1">
                                <Eye className="h-3 w-3" />
                                <span>{result.viewCount}</span>
                              </div>
                            )}
                            {result.commentCount !== undefined && result.commentCount > 0 && (
                              <div className="flex items-center gap-1">
                                <MessageCircle className="h-3 w-3" />
                                <span>{result.commentCount}</span>
                              </div>
                            )}
                          </div>

                          {result.tags && result.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-3">
                              {result.tags.slice(0, 5).map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : searchQuery && !loading ? (
              // 无结果状态
              <div className="text-center py-12">
                <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">未找到相关结果</h3>
                <p className="text-muted-foreground mb-4">
                  尝试使用不同的关键词或检查拼写
                </p>
                <Button variant="outline" onClick={() => setSearchQuery('')}>
                  清除搜索
                </Button>
              </div>
            ) : null}
          </TabsContent>
        </Tabs>
      )}

      {/* 空状态 - 未搜索时 */}
      {!searchQuery && !loading && (
        <div className="text-center py-12">
          <Search className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">开始搜索</h3>
          <p className="text-muted-foreground">
            输入关键词搜索企业、职位、面经或帖子
          </p>
        </div>
      )}
    </div>
  )
}