import { createClient } from './client'

/**
 * 测试Supabase数据库连接
 * @returns Promise<boolean> 连接是否成功
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const supabase = createClient()
    
    // 尝试获取用户信息来测试连接
    const { error } = await supabase.auth.getUser()
    
    if (error && error.message !== 'Auth session missing!') {
      console.error('Supabase连接错误:', error.message)
      return false
    }
    
    console.log('✅ Supabase连接成功')
    console.log('📊 连接信息:', {
      url: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    })
    
    return true
  } catch (error) {
    console.error('❌ Supabase连接失败:', error)
    return false
  }
}

/**
 * 检查环境变量配置
 * @returns boolean 环境变量是否正确配置
 */
export function checkEnvironmentVariables(): boolean {
  const requiredVars = {
    'NEXT_PUBLIC_SUPABASE_URL': process.env.NEXT_PUBLIC_SUPABASE_URL,
    'NEXT_PUBLIC_SUPABASE_ANON_KEY': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  }
  
  const missing = Object.entries(requiredVars)
    .filter(([, value]) => !value)
    .map(([key]) => key)
  
  if (missing.length > 0) {
    console.error('❌ 缺少必要的环境变量:', missing)
    console.log('📝 请在 .env.local 文件中配置这些变量')
    return false
  }
  
  console.log('✅ 环境变量配置完整')
  return true
} 