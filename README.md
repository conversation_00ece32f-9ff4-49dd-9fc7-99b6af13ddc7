# 🚀 WorkMates - 职场信息分享平台

> 一个现代化的职场信息分享平台，为职场人士提供真实的企业信息、薪资数据和面试经验。

## 📋 项目概述

WorkMates 是一个基于 **Next.js 15** 和 **Supabase** 构建的职场社区平台，类似于"看准网"，旨在为求职者和在职人员提供：

- 🏢 **真实企业信息** - 企业详情、工作环境、管理文化
- 💰 **透明薪资数据** - 匿名薪资分享、行业薪资对比
- 📝 **面试经验分享** - 面试流程、题目分享、难度评估
- 👥 **职场交流社区** - 职场讨论、经验分享、问题解答
- 🔍 **智能信息检索** - 企业搜索、职位搜索、内容搜索

## ✨ 核心功能

### 🏢 企业信息系统
- 企业详情查看和搜索
- 企业评分和评价系统
- 企业薪资统计和分析
- 企业面试经验汇总

### 💰 薪资数据管理
- 匿名薪资数据分享
- 多维度薪资统计分析
- 薪资趋势和对比
- 行业薪资水平参考

### 📝 面试经验分享
- 详细面试流程记录
- 面试题目和经验分享
- 面试难度评估
- 面试结果统计

### 👥 职场论坛社区
- 职场话题讨论
- 问题求助和解答
- 经验分享和交流
- 评论互动和点赞收藏

### 🔐 用户认证系统
- Google OAuth 快速登录
- 邮箱密码注册登录
- 用户资料管理
- 隐私设置控制

### 🔍 智能搜索系统
- 全局搜索功能
- 企业信息搜索
- 帖子内容搜索
- 用户信息搜索
- 高级筛选和排序

### 🔔 通知系统
- 实时通知推送
- 多类型通知管理（社交、工作、系统等）
- 通知优先级设置
- 个性化通知偏好
- 通知中心和历史记录

## 🛠️ 技术栈

### 前端技术
- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: TailwindCSS + shadcn/ui
- **UI组件**: Radix UI
- **表单**: React Hook Form + Zod
- **图标**: Lucide React

### 后端技术
- **数据库**: Supabase (PostgreSQL)
- **ORM**: Prisma
- **认证**: NextAuth.js v5
- **API**: Next.js API Routes
- **文件存储**: Cloudflare R2 对象存储

### 开发工具
- **包管理**: npm/yarn/pnpm
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript
- **部署**: Vercel
- **数据库管理**: Prisma Studio

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- npm/yarn/pnpm
- Supabase 账户

### 1. 克隆项目
```bash
git clone https://github.com/liaojiexin/WorkMates.git
cd WorkMates
```

### 2. 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 3. 环境配置
```bash
# 复制环境变量文件
cp env.example .env.local

# 编辑 .env.local 文件，配置以下变量：
# NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
# DATABASE_URL=your_database_url
# NEXTAUTH_SECRET=your_nextauth_secret
# GOOGLE_CLIENT_ID=your_google_client_id
# GOOGLE_CLIENT_SECRET=your_google_client_secret
```

### 4. 数据库配置
```bash
# 生成 Prisma 客户端
npx prisma generate

# 同步数据库结构
npx prisma db pull
```

### 5. 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 6. 测试数据库连接
```bash
# 测试数据库连接
node scripts/test-db-connection.js

# 创建测试数据
npm run db:seed
```

## 🎯 当前实现状态

### ✅ 已完成功能

#### 🔐 用户认证系统
- ✅ Google OAuth 登录
- ✅ 用户资料管理
- ✅ Session 管理
- ✅ 权限控制

#### 🏢 企业信息系统
- ✅ 企业详情页面
- ✅ 企业评价展示
- ✅ 薪资数据展示
- ✅ 面试经验展示
- ✅ 企业数据统计

#### 📝 内容发布系统
- ✅ 论坛帖子发布
- ✅ 面试经验提交
- ✅ 薪资数据提交
- ✅ 真实API集成

#### 🗄️ 数据库系统
- ✅ 完整的数据库设计
- ✅ Prisma ORM 集成
- ✅ 数据迁移和种子
- ✅ 数据验证和约束

#### 🔍 智能搜索系统
- ✅ 全局搜索功能
- ✅ 企业信息搜索
- ✅ 帖子内容搜索
- ✅ 用户信息搜索
- ✅ 高级筛选和排序
- ✅ 搜索结果统计

#### 🔔 通知系统
- ✅ 实时通知推送
- ✅ 25种通知类型（社交、工作、内容、系统、私信）
- ✅ 通知优先级管理（低、普通、高、紧急）
- ✅ 通知中心和历史记录
- ✅ 个性化通知设置
- ✅ 批量通知操作
- ✅ 通知统计和分析

#### 📁 文件存储系统
- ✅ Cloudflare R2 对象存储集成
- ✅ 头像上传功能（支持JPEG、PNG、WebP格式，最大5MB）
- ✅ 文件自动命名和去重
- ✅ 旧文件自动清理
- ✅ 用户头像系统完整实现

### 🚧 开发中功能

#### 📁 高级文件管理
- 🔄 工作证明文件上传
- 🔄 企业Logo管理
- 🔄 文档存储功能
- 🔄 文件访问权限控制

### 📊 测试数据

项目包含完整的测试数据：
- 6个企业信息
- 多条企业评价
- 薪资数据样本
- 面试经验分享
- 用户账户数据

## 📁 项目结构

```
WorkMates/
├── src/                   # 源代码目录
│   ├── app/              # Next.js 15 App Router
│   │   ├── (main)/       # 主要功能页面组
│   │   │   ├── companies/    # 企业信息页面
│   │   │   ├── forum/        # 论坛社区页面
│   │   │   └── profile/      # 用户中心页面
│   │   ├── auth/         # 认证相关页面
│   │   ├── api/          # API 路由
│   │   │   ├── auth/         # 认证 API
│   │   │   ├── companies/    # 企业 API
│   │   │   ├── posts/        # 帖子 API
│   │   │   ├── search/       # 搜索 API
│   │   │   └── notifications/ # 通知 API
│   │   ├── globals.css   # 全局样式
│   │   ├── layout.tsx    # 根布局
│   │   └── page.tsx      # 首页
│   ├── components/       # 可复用组件
│   │   ├── ui/          # 基础UI组件 (shadcn/ui)
│   │   ├── layout/      # 布局组件
│   │   ├── notifications/ # 通知系统组件
│   │   └── features/    # 功能特定组件
│   ├── lib/             # 工具库和配置
│   │   ├── auth.ts      # NextAuth.js 配置
│   │   ├── prisma.ts    # Prisma 客户端
│   │   ├── supabase/    # Supabase 客户端
│   │   ├── notifications.ts # 通知服务库
│   │   ├── search.ts    # 搜索服务库
│   │   └── utils.ts     # 工具函数
│   ├── types/           # TypeScript 类型定义
│   └── middleware.ts    # Next.js 中间件
├── prisma/              # Prisma 配置
│   ├── schema.prisma    # 数据库模型
│   └── seed.ts          # 种子数据
├── doc/                 # 项目文档
│   ├── design/          # 设计文档
│   └── sql/             # SQL 脚本
├── scripts/             # 工具脚本
│   ├── test-db-connection.js    # 数据库连接测试
│   ├── check-env.js             # 环境变量检查
│   ├── create-company-test-data.js # 创建测试数据
│   └── setup-google-oauth.js    # Google OAuth 设置
├── public/              # 静态资源
├── .env.example         # 环境变量示例
├── package.json
└── README.md
```

## 🗄️ 数据库设计

### 核心表结构

| 系统模块 | 数据表 | 主要功能 |
|---------|--------|----------|
| 用户系统 | users, accounts, sessions | 用户管理、认证、会话 |
| 企业系统 | companies | 企业信息、统计数据 |
| 内容系统 | posts, comments, likes, bookmarks | 帖子、评论、互动 |
| 数据分享 | salaries, interviews, ratings | 薪资、面试、评分 |
| 工作经历 | work_experiences, experience_files | 经历管理、文件存储 |
| 管理系统 | reports | 内容举报和审核 |

### 数据库特性
- 完整的关系型设计
- 数据完整性约束
- 自动时间戳管理
- 索引优化
- 枚举类型定义
- 软删除支持

## 🔧 环境变量配置

在 `.env.local` 文件中配置以下变量：

```env
# Supabase 数据库配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
DATABASE_URL=your_database_url

# NextAuth.js 认证配置
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Google OAuth 配置
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Cloudflare R2 配置 (可选)
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=workmates-assets
R2_PUBLIC_URL=https://your-bucket.your-account-id.r2.cloudflarestorage.com
R2_CUSTOM_DOMAIN=https://assets.workmates.com
```

## 📚 文档导航

### 设计文档
- [项目需求文档](doc/design/WorkMates项目需求文档.md) - 功能需求和业务逻辑
- [数据库设计文档](doc/design/WorkMates数据库完整设计文档.md) - 数据库结构设计
- [API接口文档](doc/design/API开发指南.md) - API接口说明

### 开发指南
- [认证系统指南](doc/design/OAuth认证系统实现指南.md) - 用户认证实现
- [Supabase配置指南](doc/design/Supabase接入配置指南.md) - 数据库配置
- [文件上传指南](doc/design/文件上传系统实现指南.md) - 文件处理系统

## 🚀 部署指南

### Vercel 部署
1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

### 环境变量配置
确保在 Vercel 中配置所有必要的环境变量。

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

- 项目地址: [https://github.com/liaojiexin/WorkMates](https://github.com/liaojiexin/WorkMates)
- 问题反馈: [GitHub Issues](https://github.com/liaojiexin/WorkMates/issues)

---

**🎊 感谢您对 WorkMates 项目的关注！**

如果您觉得这个项目有价值，请给我们一个 ⭐️ 星标！
