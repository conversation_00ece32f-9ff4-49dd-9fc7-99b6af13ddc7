'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
    AlertTriangle,
    CheckCircle,
    Heart,
    Lightbulb,
    Loader2,
    MessageSquare,
    Star
} from 'lucide-react'
import { Metadata } from 'next'
import Link from 'next/link'
import { useState } from 'react'

export const metadata: Metadata = {
  title: '意见反馈 - WorkMates',
  description: '向我们反馈您的意见和建议，帮助我们改进产品'
}

interface FeedbackFormData {
  type: string
  title: string
  description: string
  email: string
  name: string
  priority: string
}

export default function FeedbackPage() {
  const [formData, setFormData] = useState<FeedbackFormData>({
    type: '',
    title: '',
    description: '',
    email: '',
    name: '',
    priority: 'medium'
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const feedbackTypes = [
    { value: 'bug', label: '错误报告', icon: <AlertTriangle className="h-4 w-4" />, color: 'bg-red-100 text-red-800' },
    { value: 'feature', label: '功能建议', icon: <Lightbulb className="h-4 w-4" />, color: 'bg-blue-100 text-blue-800' },
    { value: 'improvement', label: '改进建议', icon: <Star className="h-4 w-4" />, color: 'bg-green-100 text-green-800' },
    { value: 'other', label: '其他反馈', icon: <MessageSquare className="h-4 w-4" />, color: 'bg-gray-100 text-gray-800' }
  ]

  const handleInputChange = (field: keyof FeedbackFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.type) newErrors.type = '请选择反馈类型'
    if (!formData.title.trim()) newErrors.title = '请输入反馈标题'
    if (!formData.description.trim()) newErrors.description = '请详细描述您的反馈'
    if (!formData.email.trim()) {
      newErrors.email = '请输入邮箱地址'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      // TODO: 实际提交到后端API
      console.log('提交反馈:', formData)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setSubmitted(true)
    } catch (error) {
      console.error('提交失败:', error)
      setErrors({ submit: '提交失败，请稍后重试' })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitted) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <Card className="text-center">
          <CardContent className="pt-8 pb-8">
            <div className="mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h1 className="text-2xl font-bold text-green-800 mb-2">反馈提交成功！</h1>
              <p className="text-gray-600">
                感谢您的宝贵意见，我们会认真考虑您的建议
              </p>
            </div>
            
            <div className="space-y-3 text-sm text-gray-500">
              <p>• 我们会在 3 个工作日内回复您的邮箱</p>
              <p>• 重要问题会优先处理</p>
              <p>• 您的反馈将帮助我们不断改进产品</p>
            </div>
            
            <div className="mt-6 space-y-3">
              <Button 
                onClick={() => {
                  setSubmitted(false)
                  setFormData({
                    type: '',
                    title: '',
                    description: '',
                    email: '',
                    name: '',
                    priority: 'medium'
                  })
                }}
                variant="outline"
              >
                再次反馈
              </Button>
              <div>
                <Button asChild>
                  <Link href="/">返回首页</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      {/* 页面头部 */}
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-primary/10 rounded-full">
            <Heart className="h-8 w-8 text-primary" />
          </div>
        </div>
        <h1 className="text-3xl font-bold mb-2">意见反馈</h1>
        <p className="text-gray-600">
          您的反馈是我们改进的动力，每一条建议都很珍贵
        </p>
      </div>

      {/* 反馈表单 */}
      <Card>
        <CardHeader>
          <CardTitle>告诉我们您的想法</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 反馈类型 */}
            <div className="space-y-2">
              <Label htmlFor="type">反馈类型 *</Label>
              <div className="grid grid-cols-2 gap-3">
                {feedbackTypes.map((type) => (
                  <button
                    key={type.value}
                    type="button"
                    onClick={() => handleInputChange('type', type.value)}
                    className={`p-3 border rounded-lg text-left transition-all hover:border-primary ${
                      formData.type === type.value 
                        ? 'border-primary bg-primary/5' 
                        : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {type.icon}
                      <span className="font-medium">{type.label}</span>
                    </div>
                    <Badge variant="secondary" className={type.color}>
                      {type.label}
                    </Badge>
                  </button>
                ))}
              </div>
              {errors.type && <p className="text-sm text-red-600">{errors.type}</p>}
            </div>

            {/* 反馈标题 */}
            <div className="space-y-2">
              <Label htmlFor="title">反馈标题 *</Label>
              <Input
                id="title"
                placeholder="简要描述您的问题或建议"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && <p className="text-sm text-red-600">{errors.title}</p>}
            </div>

            {/* 详细描述 */}
            <div className="space-y-2">
              <Label htmlFor="description">详细描述 *</Label>
              <Textarea
                id="description"
                placeholder="请详细描述您遇到的问题或建议，包括操作步骤、期望结果等"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={5}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
            </div>

            {/* 优先级 */}
            <div className="space-y-2">
              <Label htmlFor="priority">优先级</Label>
              <Select 
                value={formData.priority} 
                onValueChange={(value) => handleInputChange('priority', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">低优先级</SelectItem>
                  <SelectItem value="medium">中等优先级</SelectItem>
                  <SelectItem value="high">高优先级</SelectItem>
                  <SelectItem value="urgent">紧急</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 联系信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">姓名（可选）</Label>
                <Input
                  id="name"
                  placeholder="您的姓名"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址 *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="用于接收回复"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
              </div>
            </div>

            {/* 错误提示 */}
            {errors.submit && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{errors.submit}</AlertDescription>
              </Alert>
            )}

            {/* 提交按钮 */}
            <Button 
              type="submit" 
              className="w-full" 
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  提交中...
                </>
              ) : (
                '提交反馈'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* 其他联系方式 */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>其他联系方式</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-3 text-sm text-gray-600">
            <MessageSquare className="h-4 w-4" />
            <span>客服邮箱：<EMAIL></span>
          </div>
          <div className="flex items-center gap-3 text-sm text-gray-600">
            <MessageSquare className="h-4 w-4" />
            <span>工作时间：周一至周五 9:00-18:00</span>
          </div>
          <div className="flex items-center gap-3 text-sm text-gray-600">
            <Star className="h-4 w-4" />
            <span>紧急问题请标注&ldquo;紧急&rdquo;优先级</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 