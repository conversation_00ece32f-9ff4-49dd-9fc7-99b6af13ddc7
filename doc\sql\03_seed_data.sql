-- WorkMates 数据库种子数据
-- 版本: 1.0
-- 最后更新: 2025-07-13
-- 此文件包含用于为 WorkMates 数据库填充初始数据的种子数据。

-- 填充用户数据
INSERT INTO users (username, email, password_hash) VALUES
('john_doe', '<EMAIL>', 'hashed_password_1'), -- 示例用户1
('jane_smith', '<EMAIL>', 'hashed_password_2'); -- 示例用户2

-- 填充公司数据
INSERT INTO companies (name, industry) VALUES
('Tech Corp', '科技'), -- 示例公司1
('Innovate LLC', '软件'); -- 示例公司2

-- 填充薪资数据
INSERT INTO salaries (user_id, company_id, job_title, salary, currency) VALUES
((SELECT id FROM users WHERE username = 'john_doe'), (SELECT id FROM companies WHERE name = 'Tech Corp'), '软件工程师', 120000, 'USD'),
((SELECT id FROM users WHERE username = 'jane_smith'), (SELECT id FROM companies WHERE name = 'Innovate LLC'), '产品经理', 110000, 'USD');

-- 填充面试经验数据
INSERT INTO interviews (user_id, company_id, job_title, content, difficulty_level) VALUES
((SELECT id FROM users WHERE username = 'john_doe'), (SELECT id FROM companies WHERE name = 'Tech Corp'), '软件工程师', '包含编程挑战的技术面试。', 4),
((SELECT id FROM users WHERE username = 'jane_smith'), (SELECT id FROM companies WHERE name = 'Innovate LLC'), '产品经理', '行为问题和案例研究。', 3);

-- 填充论坛帖子数据
INSERT INTO posts (user_id, title, content) VALUES
((SELECT id FROM users WHERE username = 'john_doe'), '我在 Tech Corp 的经历', '这是一段很棒的旅程...'),
((SELECT id FROM users WHERE username = 'jane_smith'), 'Innovate LLC 的工作与生活平衡', '公司文化非常棒...');

-- 填充评论数据
INSERT INTO comments (post_id, user_id, content) VALUES
((SELECT id FROM posts WHERE title = '我在 Tech Corp 的经历'), (SELECT id FROM users WHERE username = 'jane_smith'), '感谢分享！'),
((SELECT id FROM posts WHERE title = 'Innovate LLC 的工作与生活平衡'), (SELECT id FROM users WHERE username = 'john_doe'), '我同意，这是一个很棒的工作场所。');
