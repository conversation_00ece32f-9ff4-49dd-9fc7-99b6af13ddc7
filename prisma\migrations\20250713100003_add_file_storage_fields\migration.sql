-- CreateEnum
-- 用户等级
CREATE TYPE "UserLevel" AS ENUM ('NEWBIE', 'ACTIVE', 'SENIOR', 'EXPERT', 'MODERATOR', 'ADMIN');

-- CreateEnum
-- 公司规模
CREATE TYPE "CompanySize" AS ENUM ('STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE');

-- CreateEnum
-- 帖子类型
CREATE TYPE "PostType" AS ENUM ('DISCUSSION', 'QUESTION', 'SHARING', 'NEWS', 'REVIEW', 'JOB');

-- CreateEnum
-- 面试难度
CREATE TYPE "InterviewDifficulty" AS ENUM ('EASY', 'MEDIUM', 'HARD', 'VERY_HARD');

-- CreateEnum
-- 面试结果
CREATE TYPE "InterviewResult" AS ENUM ('PASSED', 'FAILED', 'PENDING', 'CANCELLED');

-- CreateEnum
-- 举报原因
CREATE TYPE "ReportReason" AS ENUM ('SPAM', 'INAPPROPRIATE', 'FAKE_INFO', 'HARASSMENT', 'COPYRIGHT', 'OTHER');

-- CreateEnum
-- 举报状态
CREATE TYPE "ReportStatus" AS ENUM ('PENDING', 'REVIEWING', 'RESOLVED', 'REJECTED');

-- CreateEnum
-- 雇佣类型
CREATE TYPE "EmploymentType" AS ENUM ('FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERNSHIP', 'FREELANCE');

-- CreateEnum
-- 验证状态
CREATE TYPE "VerificationStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'REVOKED');

-- CreateEnum
-- 文件分类
CREATE TYPE "FileCategory" AS ENUM ('CONTRACT', 'CERTIFICATE', 'PHOTO', 'DOCUMENT', 'OTHER');

-- CreateEnum
-- 目标类型 (用于审核等)
CREATE TYPE "TargetType" AS ENUM ('WORK_EXPERIENCE', 'EXPERIENCE_FILE', 'SALARY', 'INTERVIEW');

-- CreateEnum
-- 审核决定
CREATE TYPE "ReviewDecision" AS ENUM ('APPROVED', 'REJECTED', 'REVOKED', 'PENDING_MORE_INFO');

-- CreateEnum
-- 文件存储目录
CREATE TYPE "FileFolder" AS ENUM ('AVATARS', 'COMPANY_LOGOS', 'WORK_FILES', 'DOCUMENTS', 'ATTACHMENTS', 'TEMP');

-- CreateEnum
-- 文件用途
CREATE TYPE "FilePurpose" AS ENUM ('USER_AVATAR', 'COMPANY_LOGO', 'WORK_CERTIFICATE', 'EDUCATION_CERTIFICATE', 'RESUME', 'PORTFOLIO', 'INTERVIEW_ATTACHMENT', 'SALARY_PROOF', 'OTHER');

-- CreateTable
-- 用户表
CREATE TABLE "users" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "email" VARCHAR(255) NOT NULL, -- 邮箱
    "emailVerified" TIMESTAMPTZ(6), -- 邮箱验证时间
    "username" VARCHAR(100), -- 用户名
    "phone" VARCHAR(20), -- 手机号
    "password" VARCHAR(255), -- 密码
    "name" VARCHAR(100), -- 姓名
    "image" VARCHAR(500), -- 图片
    "avatar" VARCHAR(500), -- 头像
    "avatarKey" VARCHAR(200), -- 头像文件Key
    "bio" TEXT, -- 个人简介
    "position" VARCHAR(100), -- 职位
    "company" VARCHAR(200), -- 公司
    "experience" INTEGER, -- 经验
    "industry" VARCHAR(100), -- 行业
    "education" VARCHAR(200), -- 教育背景
    "skills" TEXT[] DEFAULT ARRAY[]::TEXT[], -- 技能
    "level" "UserLevel" DEFAULT 'NEWBIE', -- 等级
    "points" INTEGER DEFAULT 0, -- 积分
    "reputation" DECIMAL(10,2) DEFAULT 0.0, -- 声望
    "storageUsed" BIGINT DEFAULT 0, -- 已用存储空间
    "storageLimit" BIGINT DEFAULT *********, -- 存储空间限制
    "profileCompleteness" INTEGER DEFAULT 0, -- 个人资料完成度
    "lastProfileUpdate" TIMESTAMPTZ(6), -- 最后一次个人资料更新时间
    "followersCount" INTEGER DEFAULT 0, -- 粉丝数
    "followingCount" INTEGER DEFAULT 0, -- 关注数
    "emailVerificationToken" VARCHAR(255), -- 邮箱验证令牌
    "passwordResetToken" VARCHAR(255), -- 密码重置令牌
    "passwordResetExpires" TIMESTAMPTZ(6), -- 密码重置令牌过期时间
    "twoFactorEnabled" BOOLEAN DEFAULT false, -- 是否启用两步验证
    "twoFactorSecret" VARCHAR(255), -- 两步验证密钥
    "isAnonymous" BOOLEAN DEFAULT false, -- 是否匿名
    "isEmailPublic" BOOLEAN DEFAULT false, -- 邮箱是否公开
    "isPhonePublic" BOOLEAN DEFAULT false, -- 手机号是否公开
    "isVerified" BOOLEAN DEFAULT false, -- 是否已验证
    "isActive" BOOLEAN DEFAULT true, -- 是否活跃
    "isBanned" BOOLEAN DEFAULT false, -- 是否被封禁
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    "lastLogin" TIMESTAMPTZ(6), -- 最后登录时间

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 第三方账户表
CREATE TABLE "accounts" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL, -- 用户ID
    "type" VARCHAR(50) NOT NULL, -- 账户类型
    "provider" VARCHAR(50) NOT NULL, -- 提供商
    "providerAccountId" VARCHAR(100) NOT NULL, -- 提供商账户ID
    "refresh_token" TEXT, -- 刷新令牌
    "access_token" TEXT, -- 访问令牌
    "expires_at" INTEGER, -- 过期时间
    "token_type" VARCHAR(50), -- 令牌类型
    "scope" VARCHAR(200), -- 范围
    "id_token" TEXT, -- ID令牌
    "session_state" VARCHAR(200), -- 会话状态

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 会话表
CREATE TABLE "sessions" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "sessionToken" VARCHAR(255) NOT NULL, -- 会话令牌
    "userId" UUID NOT NULL, -- 用户ID
    "expires" TIMESTAMPTZ(6) NOT NULL, -- 过期时间

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 验证令牌表
CREATE TABLE "verification_tokens" (
    "identifier" VARCHAR(255) NOT NULL, -- 标识符
    "token" VARCHAR(255) NOT NULL, -- 令牌
    "expires" TIMESTAMPTZ(6) NOT NULL -- 过期时间
);

-- CreateTable
-- 公司表
CREATE TABLE "companies" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" VARCHAR(200) NOT NULL, -- 公司名称
    "nameEn" VARCHAR(200), -- 公司英文名称
    "logo" VARCHAR(500), -- 公司Logo
    "description" TEXT, -- 公司描述
    "website" VARCHAR(500), -- 公司网站
    "industry" VARCHAR(100), -- 所属行业
    "size" "CompanySize", -- 公司规模
    "foundedYear" INTEGER, -- 成立年份
    "headquarters" VARCHAR(100), -- 总部
    "address" TEXT, -- 地址
    "phone" VARCHAR(50), -- 电话
    "email" VARCHAR(255), -- 邮箱
    "isVerified" BOOLEAN DEFAULT false, -- 是否已验证
    "isActive" BOOLEAN DEFAULT true, -- 是否活跃
    "totalRatings" INTEGER DEFAULT 0, -- 总评分数
    "averageRating" DECIMAL(3,2) DEFAULT 0.0, -- 平均评分
    "totalSalaries" INTEGER DEFAULT 0, -- 总薪资数
    "totalReviews" INTEGER DEFAULT 0, -- 总评论数
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 帖子表
CREATE TABLE "posts" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "title" VARCHAR(200) NOT NULL, -- 标题
    "content" TEXT NOT NULL, -- 内容
    "excerpt" VARCHAR(500), -- 摘要
    "type" "PostType" DEFAULT 'DISCUSSION', -- 类型
    "category" VARCHAR(50), -- 分类
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[], -- 标签
    "companyId" UUID, -- 公司ID
    "authorId" UUID NOT NULL, -- 作者ID
    "isAnonymous" BOOLEAN DEFAULT false, -- 是否匿名
    "isPublished" BOOLEAN DEFAULT true, -- 是否已发布
    "isPinned" BOOLEAN DEFAULT false, -- 是否置顶
    "isLocked" BOOLEAN DEFAULT false, -- 是否锁定
    "isDeleted" BOOLEAN DEFAULT false, -- 是否已删除
    "viewCount" INTEGER DEFAULT 0, -- 查看次数
    "likeCount" INTEGER DEFAULT 0, -- 点赞次数
    "commentCount" INTEGER DEFAULT 0, -- 评论次数
    "shareCount" INTEGER DEFAULT 0, -- 分享次数
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    "publishedAt" TIMESTAMPTZ(6), -- 发布时间

    CONSTRAINT "posts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 评论表
CREATE TABLE "comments" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "content" TEXT NOT NULL, -- 内容
    "postId" UUID NOT NULL, -- 帖子ID
    "authorId" UUID NOT NULL, -- 作者ID
    "parentId" UUID, -- 父评论ID
    "isAnonymous" BOOLEAN DEFAULT false, -- 是否匿名
    "isDeleted" BOOLEAN DEFAULT false, -- 是否已删除
    "likeCount" INTEGER DEFAULT 0, -- 点赞次数
    "replyCount" INTEGER DEFAULT 0, -- 回复次数
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "comments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 点赞表
CREATE TABLE "likes" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL, -- 用户ID
    "postId" UUID, -- 帖子ID
    "commentId" UUID, -- 评论ID
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间

    CONSTRAINT "likes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 书签表
CREATE TABLE "bookmarks" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL, -- 用户ID
    "postId" UUID NOT NULL, -- 帖子ID
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间

    CONSTRAINT "bookmarks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 薪资表
CREATE TABLE "salaries" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "authorId" UUID NOT NULL, -- 作者ID
    "companyId" UUID NOT NULL, -- 公司ID
    "position" VARCHAR(100) NOT NULL, -- 职位
    "level" VARCHAR(50), -- 级别
    "department" VARCHAR(100), -- 部门
    "workLocation" VARCHAR(100), -- 工作地点
    "workType" VARCHAR(50), -- 工作类型
    "experience" INTEGER, -- 经验
    "education" VARCHAR(100), -- 教育背景
    "baseSalary" DECIMAL(12,2), -- 基本工资
    "bonus" DECIMAL(12,2) DEFAULT 0, -- 奖金
    "stockOptions" DECIMAL(12,2) DEFAULT 0, -- 股票期权
    "benefits" DECIMAL(12,2) DEFAULT 0, -- 福利
    "totalSalary" DECIMAL(12,2) NOT NULL, -- 总薪资
    "currency" VARCHAR(10) DEFAULT 'CNY', -- 货币
    "salaryYear" INTEGER NOT NULL, -- 薪资年份
    "notes" TEXT, -- 备注
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[], -- 标签
    "isVerified" BOOLEAN DEFAULT false, -- 是否已验证
    "isActive" BOOLEAN DEFAULT true, -- 是否活跃
    "isAnonymous" BOOLEAN DEFAULT true, -- 是否匿名
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "salaries_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 面试表
CREATE TABLE "interviews" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "authorId" UUID NOT NULL, -- 作者ID
    "companyId" UUID NOT NULL, -- 公司ID
    "position" VARCHAR(100) NOT NULL, -- 职位
    "department" VARCHAR(100), -- 部门
    "interviewType" VARCHAR(50), -- 面试类型
    "interviewRound" INTEGER DEFAULT 1, -- 面试轮次
    "interviewDate" DATE, -- 面试日期
    "duration" INTEGER, -- 持续时间
    "difficulty" "InterviewDifficulty" NOT NULL, -- 难度
    "result" "InterviewResult" NOT NULL, -- 结果
    "rating" INTEGER, -- 评分
    "questions" TEXT[], -- 问题
    "experience" TEXT, -- 经验
    "tips" TEXT, -- 技巧
    "notes" TEXT, -- 备注
    "isActive" BOOLEAN DEFAULT true, -- 是否活跃
    "isAnonymous" BOOLEAN DEFAULT true, -- 是否匿名
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "interviews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 公司评分表
CREATE TABLE "ratings" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "authorId" UUID NOT NULL, -- 作者ID
    "companyId" UUID NOT NULL, -- 公司ID
    "overallRating" DECIMAL(2,1) NOT NULL, -- 总体评分
    "workLifeBalance" INTEGER, -- 工作与生活平衡
    "compensation" INTEGER, -- 薪酬福利
    "culture" INTEGER, -- 公司文化
    "careerGrowth" INTEGER, -- 职业发展
    "management" INTEGER, -- 管理
    "title" VARCHAR(200), -- 标题
    "pros" TEXT, -- 优点
    "cons" TEXT, -- 缺点
    "advice" TEXT, -- 建议
    "isRecommended" BOOLEAN, -- 是否推荐
    "recommendationReason" TEXT, -- 推荐原因
    "position" VARCHAR(100), -- 职位
    "department" VARCHAR(100), -- 部门
    "workDuration" INTEGER, -- 工作时长
    "employmentType" VARCHAR(50), -- 雇佣类型
    "isActive" BOOLEAN DEFAULT true, -- 是否活跃
    "isAnonymous" BOOLEAN DEFAULT true, -- 是否匿名
    "isVerified" BOOLEAN DEFAULT false, -- 是否已验证
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "ratings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 举报表
CREATE TABLE "reports" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "reporterId" UUID NOT NULL, -- 举报人ID
    "targetType" VARCHAR(50) NOT NULL, -- 目标类型
    "targetId" UUID NOT NULL, -- 目标ID
    "reason" "ReportReason" NOT NULL, -- 举报原因
    "description" TEXT, -- 描述
    "status" "ReportStatus" DEFAULT 'PENDING', -- 状态
    "handlerId" UUID, -- 处理人ID
    "handlerNotes" TEXT, -- 处理人备注
    "resolvedAt" TIMESTAMPTZ(6), -- 解决时间
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    "userId" UUID, -- 用户ID

    CONSTRAINT "reports_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 工作经历表
CREATE TABLE "work_experiences" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL, -- 用户ID
    "companyName" VARCHAR(200) NOT NULL, -- 公司名称
    "position" VARCHAR(100) NOT NULL, -- 职位
    "department" VARCHAR(100), -- 部门
    "employmentType" "EmploymentType" NOT NULL, -- 雇佣类型
    "startDate" DATE NOT NULL, -- 开始日期
    "endDate" DATE, -- 结束日期
    "isCurrent" BOOLEAN DEFAULT false, -- 是否当前工作
    "description" TEXT, -- 描述
    "achievements" TEXT[], -- 成就
    "skills" TEXT[], -- 技能
    "salary" DECIMAL(12,2), -- 薪资
    "currency" VARCHAR(10) DEFAULT 'CNY', -- 货币
    "verificationStatus" "VerificationStatus" DEFAULT 'PENDING', -- 验证状态
    "verifiedById" UUID, -- 验证人ID
    "verifiedAt" TIMESTAMPTZ(6), -- 验证时间
    "isPublic" BOOLEAN DEFAULT false, -- 是否公开
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "work_experiences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 文件表
CREATE TABLE "files" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "originalName" VARCHAR(255) NOT NULL, -- 原始文件名
    "fileName" VARCHAR(255) NOT NULL, -- 文件名
    "filePath" VARCHAR(500) NOT NULL, -- 文件路径
    "fileSize" INTEGER NOT NULL, -- 文件大小
    "mimeType" VARCHAR(100) NOT NULL, -- MIME类型
    "fileHash" VARCHAR(64) NOT NULL, -- 文件哈希
    "type" VARCHAR(50) NOT NULL, -- 类型
    "description" TEXT, -- 描述
    "relatedId" UUID, -- 关联ID
    "relatedType" VARCHAR(50), -- 关联类型
    "uploadedById" UUID NOT NULL, -- 上传者ID
    "isDeleted" BOOLEAN DEFAULT false, -- 是否已删除
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 经历文件表
CREATE TABLE "experience_files" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "workExperienceId" UUID NOT NULL, -- 工作经历ID
    "fileName" VARCHAR(255) NOT NULL, -- 文件名
    "fileUrl" VARCHAR(500) NOT NULL, -- 文件URL
    "fileSize" INTEGER, -- 文件大小
    "mimeType" VARCHAR(100), -- MIME类型
    "category" "FileCategory" NOT NULL, -- 分类
    "description" TEXT, -- 描述
    "verificationStatus" "VerificationStatus" DEFAULT 'PENDING', -- 验证状态
    "verifiedById" UUID, -- 验证人ID
    "verifiedAt" TIMESTAMPTZ(6), -- 验证时间
    "uploadedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 上传时间
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "experience_files_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 验证记录表
CREATE TABLE "verification_records" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "reviewerId" UUID NOT NULL, -- 审核人ID
    "targetType" "TargetType" NOT NULL, -- 目标类型
    "targetId" UUID NOT NULL, -- 目标ID
    "decision" "ReviewDecision" NOT NULL, -- 审核决定
    "reason" TEXT, -- 原因
    "notes" TEXT, -- 备注
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间

    CONSTRAINT "verification_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 用户信誉表
CREATE TABLE "user_credibility" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL, -- 用户ID
    "credibilityScore" DECIMAL(5,2) DEFAULT 0.0, -- 信誉分
    "verifiedWorkExperiences" INTEGER DEFAULT 0, -- 已验证工作经历数
    "verifiedSalaries" INTEGER DEFAULT 0, -- 已验证薪资数
    "verifiedInterviews" INTEGER DEFAULT 0, -- 已验证面试数
    "contributionPoints" INTEGER DEFAULT 0, -- 贡献点
    "reportCount" INTEGER DEFAULT 0, -- 举报次数
    "lastCalculatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 最后计算时间
    "createdAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "user_credibility_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 文件上传表
CREATE TABLE "file_uploads" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL, -- 用户ID
    "fileName" VARCHAR(255) NOT NULL, -- 文件名
    "originalName" VARCHAR(255) NOT NULL, -- 原始文件名
    "fileSize" BIGINT NOT NULL, -- 文件大小
    "contentType" VARCHAR(100) NOT NULL, -- 内容类型
    "r2Key" VARCHAR(500) NOT NULL, -- R2存储Key
    "r2Url" VARCHAR(500) NOT NULL, -- R2存储URL
    "folder" "FileFolder" NOT NULL, -- 文件夹
    "purpose" "FilePurpose" NOT NULL, -- 用途
    "isActive" BOOLEAN NOT NULL DEFAULT true, -- 是否活跃
    "isPublic" BOOLEAN NOT NULL DEFAULT false, -- 是否公开
    "downloadCount" INTEGER NOT NULL DEFAULT 0, -- 下载次数
    "metadata" JSONB, -- 元数据
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) NOT NULL, -- 更新时间

    CONSTRAINT "file_uploads_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- CreateIndex
CREATE UNIQUE INDEX "users_phone_key" ON "users"("phone");

-- CreateIndex
CREATE UNIQUE INDEX "unique_provider_account" ON "accounts"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_sessionToken_key" ON "sessions"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "verification_tokens_token_key" ON "verification_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "unique_identifier_token" ON "verification_tokens"("identifier", "token");

-- CreateIndex
CREATE UNIQUE INDEX "companies_name_key" ON "companies"("name");

-- CreateIndex
CREATE UNIQUE INDEX "companies_nameEn_key" ON "companies"("nameEn");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_comment_like" ON "likes"("userId", "commentId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_post_like" ON "likes"("userId", "postId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_post_bookmark" ON "bookmarks"("userId", "postId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_user_company_rating" ON "ratings"("authorId", "companyId");

-- CreateIndex
CREATE UNIQUE INDEX "unique_file_hash_user" ON "files"("fileHash", "uploadedById");

-- CreateIndex
CREATE UNIQUE INDEX "user_credibility_userId_key" ON "user_credibility"("userId");

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "posts" ADD CONSTRAINT "posts_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "posts" ADD CONSTRAINT "posts_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comments" ADD CONSTRAINT "comments_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "likes" ADD CONSTRAINT "likes_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "bookmarks" ADD CONSTRAINT "bookmarks_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "salaries" ADD CONSTRAINT "salaries_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "salaries" ADD CONSTRAINT "salaries_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ratings" ADD CONSTRAINT "ratings_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ratings" ADD CONSTRAINT "ratings_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reports" ADD CONSTRAINT "reports_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "work_experiences" ADD CONSTRAINT "work_experiences_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "experience_files" ADD CONSTRAINT "experience_files_workExperienceId_fkey" FOREIGN KEY ("workExperienceId") REFERENCES "work_experiences"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_uploads" ADD CONSTRAINT "file_uploads_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
