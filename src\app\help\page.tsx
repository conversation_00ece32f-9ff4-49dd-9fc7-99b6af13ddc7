import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
    Building,
    HelpCircle,
    Mail,
    MessageCircle,
    Phone,
    Search,
    Shield,
    User
} from 'lucide-react'
import { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '帮助中心 - WorkMates',
  description: '查找常见问题解答，获取使用指南和技术支持'
}

// FAQ 数据
const faqs = [
  {
    category: 'account',
    title: '账户相关',
    questions: [
      {
        question: '如何注册账户？',
        answer: '点击页面右上角的"注册"按钮，填写邮箱和密码即可快速注册。支持邮箱验证和手机号验证两种方式。'
      },
      {
        question: '忘记密码怎么办？',
        answer: '在登录页面点击"忘记密码"，输入注册邮箱，我们会发送重置密码邮件到您的邮箱。'
      },
      {
        question: '如何修改个人信息？',
        answer: '登录后进入"个人中心"-"设置"页面，可以修改头像、昵称、联系方式等个人信息。'
      },
      {
        question: '可以注销账户吗？',
        answer: '可以的。在个人设置页面的底部有"注销账户"选项，注销后数据将被永久删除且无法恢复。'
      }
    ]
  },
  {
    category: 'companies',
    title: '企业信息',
    questions: [
      {
        question: '如何查看企业详细信息？',
        answer: '在企业列表页面点击企业名称，或使用搜索功能找到目标企业，进入企业详情页即可查看工作环境、薪资水平、面试经验等信息。'
      },
      {
        question: '企业信息如何保证真实性？',
        answer: '我们通过用户实名认证、内容审核机制、举报系统等多重手段确保信息真实性。同时鼓励用户举报虚假信息。'
      },
      {
        question: '可以匿名发布企业评价吗？',
        answer: '是的，我们支持匿名发布企业评价和面试经验，保护用户隐私的同时分享真实体验。'
      },
      {
        question: '如何联系企业？',
        answer: '企业详情页提供了官方网站、招聘邮箱等联系方式。部分企业还开通了在线沟通功能。'
      }
    ]
  },
  {
    category: 'forum',
    title: '社区论坛',
    questions: [
      {
        question: '如何发帖和回复？',
        answer: '注册登录后，在论坛页面点击"发布新帖"按钮即可发帖。在帖子详情页可以进行回复和讨论。'
      },
      {
        question: '什么内容不能发布？',
        answer: '禁止发布违法、虚假、恶意营销、人身攻击等内容。详细规则请查看社区公约。'
      },
      {
        question: '如何获得更多关注？',
        answer: '发布高质量、有价值的内容，积极参与讨论，帮助其他用户解答问题，逐步建立个人影响力。'
      },
      {
        question: '帖子被删除了怎么办？',
        answer: '如认为误删，可通过站内信或客服邮箱申诉。我们会在3个工作日内审核并回复。'
      }
    ]
  },
  {
    category: 'privacy',
    title: '隐私安全',
    questions: [
      {
        question: '个人信息会被泄露吗？',
        answer: '我们严格遵守隐私保护法规，采用加密存储、权限控制等技术手段保护用户信息安全。'
      },
      {
        question: '如何设置隐私权限？',
        answer: '在个人设置页面可以调整资料可见性、消息接收偏好等隐私设置。'
      },
      {
        question: '匿名发布如何保证匿名性？',
        answer: '匿名发布的内容不会显示用户真实身份信息，但平台保留必要的追溯能力以防范恶意行为。'
      }
    ]
  }
]

// 使用指南数据
const guides = [
  {
    title: '新手入门',
    description: '快速了解 WorkMates 的主要功能',
    steps: [
      '注册并完善个人信息',
      '浏览感兴趣的企业信息',
      '查看薪资数据和面试经验',
      '参与社区讨论，分享经验'
    ]
  },
  {
    title: '求职者指南',
    description: '充分利用平台信息助力求职',
    steps: [
      '使用搜索功能查找目标企业',
      '查看企业评价和薪资水平',
      '阅读面试经验分享',
      '在论坛提问获取建议'
    ]
  },
  {
    title: '分享者指南',
    description: '分享经验帮助他人的同时建立影响力',
    steps: [
      '分享真实的工作体验',
      '发布有价值的面试经验',
      '参与社区讨论和问答',
      '保持内容的客观和建设性'
    ]
  }
]

export default function HelpPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面头部 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">帮助中心</h1>
        <p className="text-xl text-muted-foreground mb-8">
          查找常见问题解答，获取使用指南和技术支持
        </p>
        
        {/* 搜索栏 */}
        <div className="max-w-md mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索帮助内容..."
              className="pl-9"
            />
          </div>
        </div>
      </div>

      <Tabs defaultValue="faq" className="max-w-4xl mx-auto">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="faq" className="flex items-center gap-2">
            <HelpCircle className="h-4 w-4" />
            常见问题
          </TabsTrigger>
          <TabsTrigger value="guides" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            使用指南
          </TabsTrigger>
          <TabsTrigger value="contact" className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            联系我们
          </TabsTrigger>
        </TabsList>

        {/* 常见问题 */}
        <TabsContent value="faq" className="mt-8">
          <div className="space-y-6">
            {faqs.map((category) => (
              <Card key={category.category}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {category.category === 'account' && <User className="h-5 w-5" />}
                    {category.category === 'companies' && <Building className="h-5 w-5" />}
                    {category.category === 'forum' && <MessageCircle className="h-5 w-5" />}
                    {category.category === 'privacy' && <Shield className="h-5 w-5" />}
                    {category.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {category.questions.map((faq, index) => (
                    <div key={index} className="border-l-2 border-primary/20 pl-4">
                      <h4 className="font-semibold text-primary mb-2">{faq.question}</h4>
                      <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 使用指南 */}
        <TabsContent value="guides" className="mt-8">
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-1">
            {guides.map((guide, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle>{guide.title}</CardTitle>
                  <p className="text-muted-foreground">{guide.description}</p>
                </CardHeader>
                <CardContent>
                  <ol className="space-y-3">
                    {guide.steps.map((step, stepIndex) => (
                      <li key={stepIndex} className="flex items-start gap-3">
                        <Badge variant="outline" className="min-w-6 h-6 rounded-full flex items-center justify-center">
                          {stepIndex + 1}
                        </Badge>
                        <span className="text-muted-foreground">{step}</span>
                      </li>
                    ))}
                  </ol>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 联系我们 */}
        <TabsContent value="contact" className="mt-8">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>在线客服</CardTitle>
                <p className="text-muted-foreground">
                  工作时间：周一至周五 9:00-18:00
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full">
                  <MessageCircle className="mr-2 h-4 w-4" />
                  在线咨询
                </Button>
                <p className="text-sm text-muted-foreground text-center">
                  平均响应时间：5分钟内
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>其他联系方式</CardTitle>
                <p className="text-muted-foreground">
                  我们会在24小时内回复您的问题
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Mail className="h-4 w-4 text-primary" />
                  <div>
                    <p className="font-medium">客服邮箱</p>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Phone className="h-4 w-4 text-primary" />
                  <div>
                    <p className="font-medium">客服热线</p>
                    <p className="text-sm text-muted-foreground">************</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>意见反馈</CardTitle>
              <p className="text-muted-foreground">
                您的建议是我们改进的动力
              </p>
            </CardHeader>
            <CardContent>
              <Button asChild>
                <Link href="/feedback">
                  提交意见反馈
                </Link>
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 