{"version": "0.2.0", "configurations": [{"name": "🚀 Next.js: 服务端调试", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev"], "cwd": "${workspaceFolder}", "skipFiles": ["<node_internals>/**"], "env": {"NODE_OPTIONS": "--inspect"}, "console": "integratedTerminal", "runtimeExecutable": "node"}, {"name": "🔗 Next.js: 连接到运行中的服务器", "type": "node", "request": "attach", "port": 9229, "skipFiles": ["<node_internals>/**"], "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}"}, {"name": "🌐 Next.js: 客户端调试 (简化版)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev"], "console": "integratedTerminal", "serverReadyAction": {"pattern": "- Local:.+(https?://localhost:[0-9]+)", "uriFormat": "%s", "action": "openExternally"}}]}