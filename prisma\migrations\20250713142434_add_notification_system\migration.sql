-- CreateEnum
-- 通知类型
CREATE TYPE "NotificationType" AS ENUM ('LIKE', 'COMMENT', 'REPLY', 'FOLLOW', 'MENTION', 'JOB_INVITATION', 'INTERVIEW_INVITE', 'SALARY_REQUEST', 'COMPANY_UPDATE', 'POST_FEATURED', 'POST_APPROVED', 'POST_REJECTED', 'CONTENT_REPORTED', 'SYSTEM_UPDATE', 'SECURITY_ALERT', 'ACCOUNT_VERIFIED', 'POLICY_UPDATE', 'MAINTENANCE', 'PRIVATE_MESSAGE', 'GROUP_MESSAGE');

-- CreateEnum
-- 通知优先级
CREATE TYPE "NotificationPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- CreateEnum
-- 通知状态
CREATE TYPE "NotificationStatus" AS ENUM ('UNREAD', 'READ', 'ARCHIVED');

-- CreateTable
-- 通知表
CREATE TABLE "notifications" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL, -- 用户ID
    "type" "NotificationType" NOT NULL, -- 类型
    "title" VARCHAR(200) NOT NULL, -- 标题
    "content" TEXT, -- 内容
    "status" "NotificationStatus" NOT NULL DEFAULT 'UNREAD', -- 状态
    "priority" "NotificationPriority" NOT NULL DEFAULT 'NORMAL', -- 优先级
    "relatedId" UUID, -- 关联ID
    "relatedType" VARCHAR(50), -- 关联类型
    "senderId" UUID, -- 发送者ID
    "senderName" VARCHAR(100), -- 发送者名称
    "senderAvatar" VARCHAR(500), -- 发送者头像
    "actionUrl" VARCHAR(500), -- 操作URL
    "actionText" VARCHAR(50), -- 操作文本
    "metadata" JSONB, -- 元数据
    "readAt" TIMESTAMPTZ(6), -- 已读时间
    "archivedAt" TIMESTAMPTZ(6), -- 归档时间
    "expiresAt" TIMESTAMPTZ(6), -- 过期时间
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
-- 通知设置表
CREATE TABLE "notification_settings" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL, -- 用户ID
    "enableLike" BOOLEAN NOT NULL DEFAULT true, -- 启用点赞通知
    "enableComment" BOOLEAN NOT NULL DEFAULT true, -- 启用评论通知
    "enableReply" BOOLEAN NOT NULL DEFAULT true, -- 启用回复通知
    "enableFollow" BOOLEAN NOT NULL DEFAULT true, -- 启用关注通知
    "enableMention" BOOLEAN NOT NULL DEFAULT true, -- 启用提及通知
    "enableJobInvitation" BOOLEAN NOT NULL DEFAULT true, -- 启用工作邀请通知
    "enableInterviewInvite" BOOLEAN NOT NULL DEFAULT true, -- 启用面试邀请通知
    "enableSalaryRequest" BOOLEAN NOT NULL DEFAULT true, -- 启用薪资请求通知
    "enableCompanyUpdate" BOOLEAN NOT NULL DEFAULT false, -- 启用公司更新通知
    "enablePostFeatured" BOOLEAN NOT NULL DEFAULT true, -- 启用帖子精选通知
    "enablePostApproved" BOOLEAN NOT NULL DEFAULT true, -- 启用帖子批准通知
    "enablePostRejected" BOOLEAN NOT NULL DEFAULT true, -- 启用帖子拒绝通知
    "enableContentReported" BOOLEAN NOT NULL DEFAULT true, -- 启用内容举报通知
    "enableSystemUpdate" BOOLEAN NOT NULL DEFAULT true, -- 启用系统更新通知
    "enableSecurityAlert" BOOLEAN NOT NULL DEFAULT true, -- 启用安全警报通知
    "enableAccountVerified" BOOLEAN NOT NULL DEFAULT true, -- 启用账户验证通知
    "enablePolicyUpdate" BOOLEAN NOT NULL DEFAULT false, -- 启用政策更新通知
    "enableMaintenance" BOOLEAN NOT NULL DEFAULT false, -- 启用维护通知
    "enablePrivateMessage" BOOLEAN NOT NULL DEFAULT true, -- 启用私信通知
    "enableGroupMessage" BOOLEAN NOT NULL DEFAULT true, -- 启用群消息通知
    "enableWebNotification" BOOLEAN NOT NULL DEFAULT true, -- 启用网页通知
    "enableEmailNotification" BOOLEAN NOT NULL DEFAULT false, -- 启用邮件通知
    "enablePushNotification" BOOLEAN NOT NULL DEFAULT false, -- 启用推送通知
    "quietHoursStart" VARCHAR(5), -- 免打扰开始时间
    "quietHoursEnd" VARCHAR(5), -- 免打扰结束时间
    "enableQuietHours" BOOLEAN NOT NULL DEFAULT false, -- 启用免打扰
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    "updatedAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间

    CONSTRAINT "notification_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "notifications_userId_status_createdAt_idx" ON "notifications"("userId", "status", "createdAt");

-- CreateIndex
CREATE INDEX "notifications_userId_type_idx" ON "notifications"("userId", "type");

-- CreateIndex
CREATE INDEX "notifications_relatedId_relatedType_idx" ON "notifications"("relatedId", "relatedType");

-- CreateIndex
CREATE INDEX "notifications_createdAt_idx" ON "notifications"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "notification_settings_userId_key" ON "notification_settings"("userId");

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_senderId_fkey" FOREIGN KEY ("senderId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification_settings" ADD CONSTRAINT "notification_settings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
