'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  Building2,
  Calendar,
  FileText,
  Edit,
  Trash2,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Ban,
  Shield,
  AlertCircle,
  Download,
} from 'lucide-react'
import {
  WorkExperience,
  ExperienceFile,
  UserCredibility,
  getVerificationStatusConfig,
  getCredibilityLevelConfig,
  formatDateRange,
  calculateExperienceDuration,
  formatFileSize,
} from '@/types/work-experience'
import { WorkExperienceForm } from '@/components/features/work-experience/work-experience-form'
import { FileUploadDialog } from '@/components/features/work-experience/file-upload-dialog'

/**
 * 工作经历管理页面
 * 用户可以添加、编辑、删除工作经历，上传相关文件
 */
export default function WorkExperiencePage() {
  const [activeTab, setActiveTab] = useState('experiences')
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showFileDialog, setShowFileDialog] = useState(false)
  const [selectedExperience, setSelectedExperience] =
    useState<WorkExperience | null>(null)
  const [selectedFiles, setSelectedFiles] = useState<ExperienceFile[]>([])

  // 模拟数据 - 实际项目中应该从API获取
  const [experiences] = useState<WorkExperience[]>([
    {
      id: '1',
      userId: 'user1',
      companyName: '阿里巴巴集团',
      position: '前端开发工程师',
      department: '技术部',
      employmentType: 'FULL_TIME',
      startDate: new Date('2021-06-01'),
      endDate: new Date('2023-08-31'),
      isCurrent: false,
      description: '负责电商平台前端开发，使用React技术栈',
      responsibilities: ['前端架构设计', '组件库开发', '性能优化'],
      achievements: ['提升页面加载速度30%', '开发通用组件库'],
      skillsUsed: ['React', 'TypeScript', 'Webpack'],
      salaryRange: '25k-35k',
      salaryCurrency: 'CNY',
      verificationStatus: 'APPROVED',
      verificationScore: 0.85,
      createdAt: new Date('2023-09-01'),
      updatedAt: new Date('2023-09-01'),
      verifiedAt: new Date('2023-09-02'),
      files: [
        {
          id: 'f1',
          workExperienceId: '1',
          fileName: 'contract_001.pdf',
          fileOriginalName: '阿里巴巴-劳动合同.pdf',
          filePath: '/uploads/files/contract_001.pdf',
          fileSize: 2048576,
          fileType: 'application/pdf',
          fileCategory: 'CONTRACT',
          title: '劳动合同',
          verificationStatus: 'APPROVED',
          isPublic: false,
          uploadedAt: new Date('2023-09-01'),
          verifiedAt: new Date('2023-09-02'),
        },
      ],
    },
    {
      id: '2',
      userId: 'user1',
      companyName: '字节跳动',
      position: '高级前端工程师',
      department: '产品技术部',
      employmentType: 'FULL_TIME',
      startDate: new Date('2023-09-01'),
      endDate: undefined,
      isCurrent: true,
      description: '负责抖音前端业务开发',
      responsibilities: ['业务需求开发', '技术方案设计'],
      achievements: ['优化核心流程转化率'],
      skillsUsed: ['Vue.js', 'Node.js', 'GraphQL'],
      salaryRange: '35k-45k',
      salaryCurrency: 'CNY',
      verificationStatus: 'PENDING',
      verificationScore: 0.0,
      createdAt: new Date('2023-10-01'),
      updatedAt: new Date('2023-10-01'),
      files: [],
    },
  ])

  const [credibility] = useState<UserCredibility>({
    id: 'c1',
    userId: 'user1',
    overallScore: 0.75,
    workExperienceScore: 0.85,
    salaryContributionScore: 0.7,
    interviewContributionScore: 0.65,
    verifiedExperiencesCount: 1,
    verifiedFilesCount: 1,
    verifiedSalariesCount: 3,
    verifiedInterviewsCount: 2,
    totalContributionsCount: 8,
    helpfulContributionsCount: 6,
    flaggedContributionsCount: 0,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-10-01'),
    lastCalculatedAt: new Date('2023-10-01'),
  })

  const handleEditExperience = (experience: WorkExperience) => {
    setSelectedExperience(experience)
    setShowEditDialog(true)
  }

  const handleViewFiles = (experience: WorkExperience) => {
    setSelectedExperience(experience)
    setSelectedFiles(experience.files || [])
    setShowFileDialog(true)
  }

  const handleDeleteExperience = async (id: string) => {
    if (confirm('确定要删除这个工作经历吗？')) {
      // TODO: 调用删除API
      console.log('删除工作经历:', id)
    }
  }

  const credibilityLevel = getCredibilityLevelConfig(credibility.overallScore)

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* 页面标题和信誉显示 */}
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">工作经历管理</h1>
          <p className="text-gray-600">管理你的工作经历，提升账户可信度</p>
        </div>

        {/* 用户信誉卡片 */}
        <Card className="mt-4 md:mt-0 md:w-80">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2">
                  <Shield
                    className={`h-5 w-5 text-${credibilityLevel.color}-600`}
                  />
                  <span className="font-semibold">
                    {credibilityLevel.label}
                  </span>
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  信誉分数: {(credibility.overallScore * 100).toFixed(1)}%
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">
                  已验证: {credibility.verifiedExperiencesCount}/
                  {experiences.length}
                </div>
                <div className="text-xs text-gray-500">
                  贡献: {credibility.totalContributionsCount}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-2 bg-gray-100 p-1 rounded-lg">
          <TabsTrigger
            value="experiences"
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <Building2 className="h-4 w-4" />
            <span>工作经历</span>
          </TabsTrigger>
          <TabsTrigger
            value="files"
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
          >
            <FileText className="h-4 w-4" />
            <span>相关文件</span>
          </TabsTrigger>
        </TabsList>

        {/* 工作经历列表 */}
        <TabsContent value="experiences" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold">我的工作经历</h2>
              <p className="text-sm text-gray-600 mt-1">
                添加详细的工作经历有助于提升你的信誉度和影响力
              </p>
            </div>
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              添加经历
            </Button>
          </div>

          {/* 审核状态提示 */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              工作经历需要管理员审核才能生效。请上传相关证明文件以提高审核通过率。
            </AlertDescription>
          </Alert>

          {/* 工作经历卡片列表 */}
          <div className="space-y-4">
            {experiences.map(experience => (
              <ExperienceCard
                key={experience.id}
                experience={experience}
                onEdit={handleEditExperience}
                onDelete={handleDeleteExperience}
                onViewFiles={handleViewFiles}
              />
            ))}
          </div>

          {experiences.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>还没有添加工作经历</p>
              <p className="text-sm mt-1">添加你的工作经历来提升账户可信度</p>
              <Button
                className="mt-4"
                variant="outline"
                onClick={() => setShowAddDialog(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                添加第一个工作经历
              </Button>
            </div>
          )}
        </TabsContent>

        {/* 文件管理 */}
        <TabsContent value="files" className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold">相关文件</h2>
            <p className="text-sm text-gray-600 mt-1">
              查看和管理你上传的工作经历相关文件
            </p>
          </div>

          {/* 文件列表 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {experiences
              .flatMap(exp => exp.files || [])
              .map(file => (
                <FileCard key={file.id} file={file} />
              ))}
          </div>

          {experiences.flatMap(exp => exp.files || []).length === 0 && (
            <div className="text-center py-12 text-gray-500">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>还没有上传任何文件</p>
              <p className="text-sm mt-1">为工作经历添加证明文件</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* 对话框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>添加工作经历</DialogTitle>
          </DialogHeader>
          <WorkExperienceForm
            onSubmit={() => setShowAddDialog(false)}
            onCancel={() => setShowAddDialog(false)}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>编辑工作经历</DialogTitle>
          </DialogHeader>
          <WorkExperienceForm
            experience={selectedExperience}
            onSubmit={() => setShowEditDialog(false)}
            onCancel={() => setShowEditDialog(false)}
          />
        </DialogContent>
      </Dialog>

      <FileUploadDialog
        open={showFileDialog}
        onOpenChange={setShowFileDialog}
        experience={selectedExperience}
        files={selectedFiles}
      />
    </div>
  )
}

/**
 * 工作经历卡片组件
 */
interface ExperienceCardProps {
  experience: WorkExperience
  onEdit: (experience: WorkExperience) => void
  onDelete: (id: string) => void
  onViewFiles: (experience: WorkExperience) => void
}

function ExperienceCard({
  experience,
  onEdit,
  onDelete,
  onViewFiles,
}: ExperienceCardProps) {
  const statusConfig = getVerificationStatusConfig(
    experience.verificationStatus
  )
  const duration = calculateExperienceDuration(
    experience.startDate,
    experience.endDate
  )

  const StatusIcon =
    {
      Clock: Clock,
      CheckCircle: CheckCircle,
      XCircle: XCircle,
      Ban: Ban,
    }[statusConfig.icon] || Clock

  return (
    <Card className="relative overflow-hidden">
      {/* 审核状态指示条 */}
      <div
        className={`absolute top-0 left-0 right-0 h-1 bg-${statusConfig.color}-500`}
      />

      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <CardTitle className="text-lg">{experience.position}</CardTitle>
              <Badge
                variant={
                  statusConfig.color === 'green' ? 'default' : 'secondary'
                }
              >
                <StatusIcon className="w-3 h-3 mr-1" />
                {statusConfig.label}
              </Badge>
            </div>
            <div className="text-gray-600">
              <div className="flex items-center gap-1 text-sm">
                <Building2 className="w-4 h-4" />
                {experience.companyName}
                {experience.department && ` • ${experience.department}`}
              </div>
              <div className="flex items-center gap-1 text-sm mt-1">
                <Calendar className="w-4 h-4" />
                {formatDateRange(
                  experience.startDate,
                  experience.endDate,
                  experience.isCurrent
                )}
                <span className="text-gray-500">（{duration}）</span>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewFiles(experience)}
              title="查看文件"
            >
              <FileText className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(experience)}
              title="编辑"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(experience.id)}
              title="删除"
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* 工作描述 */}
        {experience.description && (
          <p className="text-sm text-gray-700 mb-3">{experience.description}</p>
        )}

        {/* 技能标签 */}
        {experience.skillsUsed && experience.skillsUsed.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {experience.skillsUsed.map((skill, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {skill}
              </Badge>
            ))}
          </div>
        )}

        {/* 底部信息 */}
        <div className="flex justify-between items-center text-xs text-gray-500">
          <div className="flex gap-4">
            {experience.files && experience.files.length > 0 && (
              <span className="flex items-center gap-1">
                <FileText className="w-3 h-3" />
                {experience.files.length} 个文件
              </span>
            )}
            {experience.salaryRange && (
              <span>薪资: {experience.salaryRange}</span>
            )}
          </div>
          <div>创建于 {experience.createdAt.toLocaleDateString()}</div>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * 文件卡片组件
 */
interface FileCardProps {
  file: ExperienceFile
}

function FileCard({ file }: FileCardProps) {
  const statusConfig = getVerificationStatusConfig(file.verificationStatus)

  const StatusIcon =
    {
      Clock: Clock,
      CheckCircle: CheckCircle,
      XCircle: XCircle,
      Ban: Ban,
    }[statusConfig.icon] || Clock

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex-1 min-w-0">
            <div
              className="font-medium text-sm truncate"
              title={file.fileOriginalName}
            >
              {file.title || file.fileOriginalName}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {formatFileSize(file.fileSize)} •{' '}
              {file.fileType.split('/')[1]?.toUpperCase()}
            </div>
          </div>
          <Badge
            variant={statusConfig.color === 'green' ? 'default' : 'secondary'}
            className="ml-2"
          >
            <StatusIcon className="w-3 h-3 mr-1" />
            {statusConfig.label}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {file.description && (
          <p className="text-xs text-gray-600 mb-3 line-clamp-2">
            {file.description}
          </p>
        )}

        <div className="flex justify-between items-center">
          <span className="text-xs text-gray-500">
            {file.uploadedAt.toLocaleDateString()}
          </span>
          <div className="flex gap-1">
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <Eye className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <Download className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
